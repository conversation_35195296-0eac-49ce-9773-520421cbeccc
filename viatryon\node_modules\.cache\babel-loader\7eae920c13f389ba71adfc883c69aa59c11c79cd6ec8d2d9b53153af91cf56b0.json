{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\test\\\\viatryon\\\\src\\\\pages\\\\Tryon.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { QRCodeSVG } from 'qrcode.react'; // Fix the import\nimport uSha<PERSON><PERSON>utter from '../utils/uShapeCutter';\nimport { removeProductBackground } from '../utils/backgroundRemover';\n\n// Add CSS for range slider styling\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  /* Switch styles */\n  .switch-container {\n    position: relative;\n    display: inline-block;\n    width: 60px;\n    height: 34px;\n  }\n\n  .switch-container input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n\n  .switch-slider {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.2);\n    transition: .4s;\n    border-radius: 34px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .switch-slider:before {\n    position: absolute;\n    content: \"\";\n    height: 26px;\n    width: 26px;\n    left: 2px;\n    bottom: 2px;\n    background-color: white;\n    transition: .4s;\n    border-radius: 50%;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  }\n\n  input:checked + .switch-slider {\n    background-color: #2D8C88;\n  }\n\n  input:checked + .switch-slider:before {\n    transform: translateX(26px);\n  }\n\n  input:disabled + .switch-slider {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  .switch-label {\n    font-size: 12px;\n    font-weight: 700;\n    color: white;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);\n    margin-bottom: 8px;\n    letter-spacing: 0.5px;\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\nconst Tryon = ({\n  onBackToHome\n}) => {\n  _s();\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(false);\n\n  // URL parameters state\n  const [urlParams, setUrlParams] = useState({\n    image: null,\n    client: null,\n    size: null,\n    type: null\n  });\n\n  // Analytics tracking state\n  const [sessionId, setSessionId] = useState(null);\n  const [sessionStartTime, setSessionStartTime] = useState(null);\n  const [interactions, setInteractions] = useState([]);\n  const [behaviorMetrics, setBehaviorMetrics] = useState({\n    timeToFirstInteraction: null,\n    productSwitches: 0,\n    backgroundRemovalSuccess: false,\n    handDetectionSuccess: false,\n    totalInteractions: 0,\n    cameraInitSuccess: false,\n    engagementScore: 0,\n    scrollDepth: 0,\n    mouseMovements: 0,\n    hoverEvents: [],\n    gestureEvents: [],\n    featureUsage: {\n      cameraToggle: 0,\n      productRotation: 0,\n      sizeAdjustment: 0,\n      colorChange: 0,\n      screenshot: 0,\n      share: 0,\n      zoom: 0\n    },\n    exitIntent: {\n      detected: false,\n      timestamp: null,\n      beforeConversion: false\n    },\n    attentionMetrics: {\n      focusTime: 0,\n      blurEvents: 0,\n      returnEvents: 0,\n      idleTime: 0\n    }\n  });\n\n  // Enhanced tracking state\n  const [performanceMetrics, setPerformanceMetrics] = useState({\n    frameRate: {\n      average: 0,\n      min: 0,\n      max: 0,\n      drops: 0\n    },\n    memoryUsage: {\n      used: 0,\n      total: 0\n    },\n    networkMetrics: {},\n    renderMetrics: {},\n    resourceLoadTimes: []\n  });\n  const [qualityMetrics, setQualityMetrics] = useState({\n    handDetectionAccuracy: 0,\n    backgroundRemovalQuality: 0,\n    productFitAccuracy: 0,\n    userSatisfactionScore: 0,\n    technicalIssues: []\n  });\n  const [conversionFunnel, setConversionFunnel] = useState({\n    viewedProduct: true,\n    initiatedTryOn: false,\n    completedTryOn: false,\n    sharedResult: false,\n    addedToCart: false,\n    proceededToCheckout: false,\n    completedPurchase: false\n  });\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // Single gender role\n  const [userWristSize, setUserWristSize] = useState(50); // Default wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Intro popup state\n  const [showIntroPopup, setShowIntroPopup] = useState(true);\n\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [{\n    name: \"Classic Black\",\n    path: \"watches/watch_1.png\",\n    // Rolex Submariner style - 40mm case\n    caseDiameter: 41,\n    // mm\n    caseThickness: 12.5,\n    // mm\n    totalWidth: 42,\n    // mm (including crown)\n    totalHeight: 47,\n    // mm (lug to lug)\n    dialDiameter: 31,\n    // mm (visible dial)\n    type: \"dress\",\n    dialSize: 40\n  }, {\n    name: \"Silver Chrono\",\n    path: \"watches/watch_2.png\",\n    // Omega Speedmaster style - 42mm case\n    caseDiameter: 42,\n    // mm\n    caseThickness: 13.2,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 48.5,\n    // mm\n    dialDiameter: 33,\n    // mm\n    type: \"sport\",\n    dialSize: 42\n  }, {\n    name: \"Gold Luxury\",\n    path: \"watches/watch_3.png\",\n    // Patek Philippe Calatrava style - 38mm case\n    caseDiameter: 39,\n    // mm\n    caseThickness: 8.5,\n    // mm\n    totalWidth: 39,\n    // mm\n    totalHeight: 45,\n    // mm\n    dialDiameter: 30,\n    // mm\n    type: \"luxury\",\n    dialSize: 38\n  }, {\n    name: \"Sport Blue\",\n    path: \"watches/watch_6.png\",\n    // Apple Watch style - 44mm case\n    caseDiameter: 41,\n    // mm (width)\n    caseThickness: 10.7,\n    // mm\n    totalWidth: 44,\n    // mm\n    totalHeight: 38,\n    // mm (height - rectangular)\n    dialDiameter: 35,\n    // mm (screen diagonal)\n    type: \"smartwatch\",\n    dialSize: 44\n  }, {\n    name: \"Minimalist\",\n    path: \"watches/watch_5.png\",\n    // Daniel Wellington style - 36mm case\n    caseDiameter: 36,\n    // mm\n    caseThickness: 6,\n    // mm\n    totalWidth: 37,\n    // mm\n    totalHeight: 43,\n    // mm\n    dialDiameter: 28,\n    // mm\n    type: \"minimalist\",\n    dialSize: 36\n  }, {\n    name: \"Rose Gold\",\n    path: \"watches/watch_4.png\",\n    // Michael Kors style - 39mm case\n    caseDiameter: 44,\n    // mm\n    caseThickness: 11,\n    // mm\n    totalWidth: 41,\n    // mm\n    totalHeight: 46,\n    // mm\n    dialDiameter: 31,\n    // mm\n    type: \"fashion\",\n    dialSize: 41\n  }];\n  const bracelets = [{\n    name: \"Silver Chain\",\n    path: \"bracelets/bracelet_1.png\"\n  }, {\n    name: \"Gold Bangle\",\n    path: \"bracelets/bracelet_2.png\"\n  }, {\n    name: \"Leather Wrap\",\n    path: \"bracelets/bracelet_3.png\"\n  }, {\n    name: \"Diamond Tennis\",\n    path: \"bracelets/bracelet_4.png\"\n  }, {\n    name: \"Beaded Stone\",\n    path: \"bracelets/bracelet_5.png\"\n  }, {\n    name: \"Charm Bracelet\",\n    path: \"bracelets/bracelet_6.png\"\n  }];\n\n  // Enhanced background removal function with proper bracelet processing sequence\n  const removeBackground = async (imgElement, productType = 'watch') => {\n    try {\n      let processedImageUrl = imgElement.src;\n\n      // For bracelets: Apply U-shape cutter → Background removal → Vertical flip\n      if (productType === 'bracelets') {\n        try {\n          // Step 1: Apply U-shape cutter first\n          processedImageUrl = await uShapeCutter(imgElement.src);\n          console.log('U-shape cutting applied successfully for bracelet');\n\n          // Step 2: Remove background after U-shape cutting\n          processedImageUrl = await removeProductBackground(processedImageUrl, productType);\n          console.log('Background removal applied after U-shape cutting');\n\n          // Step 3: Apply vertical flip based on hand detection (will be handled in transform)\n          imgElement.src = processedImageUrl;\n        } catch (error) {\n          console.warn('Bracelet processing sequence failed:', error);\n          // Fallback to basic processing\n          await basicBackgroundRemoval(imgElement, productType);\n        }\n      } else {\n        // For watches: Just apply background removal\n        const finalProcessedUrl = await removeProductBackground(processedImageUrl, productType);\n        imgElement.src = finalProcessedUrl;\n      }\n\n      // Apply product-specific styling that preserves all colors\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n      console.log(`Background removal completed for ${productType}`);\n    } catch (error) {\n      console.warn('Enhanced background removal failed:', error);\n      // Fallback to basic background removal\n      await basicBackgroundRemoval(imgElement, productType);\n    }\n  };\n\n  // Fallback basic background removal\n  const basicBackgroundRemoval = async (imgElement, productType) => {\n    try {\n      console.log(`Applying fallback background removal for ${productType}`);\n      if (productType === 'bracelet') {\n        // Apply U-shape cutter for bracelets\n        const uShapedImage = await uShapeCutter(imgElement.src);\n        imgElement.src = uShapedImage;\n      }\n\n      // Basic background removal with conservative settings\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = imgElement.naturalWidth;\n      canvas.height = imgElement.naturalHeight;\n      ctx.drawImage(imgElement, 0, 0);\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Process each pixel - only remove very pure white backgrounds\n      for (let i = 0; i < data.length; i += 4) {\n        const r = data[i];\n        const g = data[i + 1];\n        const b = data[i + 2];\n\n        // Only remove very white pixels (more conservative for watches)\n        const threshold = productType === 'watch' ? 252 : 248;\n        if (r > threshold && g > threshold && b > threshold && Math.abs(r - g) < 3 && Math.abs(g - b) < 3) {\n          data[i + 3] = 0; // Make transparent\n        }\n      }\n      ctx.putImageData(imageData, 0, 0);\n      imgElement.src = canvas.toDataURL('image/png');\n      console.log(`Fallback background removal completed for ${productType}`);\n    } catch (error) {\n      console.error('Basic background removal failed:', error);\n    }\n  };\n\n  // Hand detection logic from tryon.js\n  const detectHandOrientation = imageData => {\n    // Simple heuristic for demo purposes - can be enhanced with ML models\n    return Math.random() > 0.5;\n  };\n\n  // Initialize camera\n  const initCamera = async () => {\n    setIsCameraReady(false);\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1920\n          },\n          height: {\n            ideal: 1080\n          }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Add camera ready state\n  const [isCameraReady, setIsCameraReady] = useState(false);\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current || !isCameraReady) return null;\n    const video = videoRef.current;\n    if (!video.videoWidth || !video.videoHeight) return null;\n    const canvas = canvasRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n  // Parse URL parameters on component mount\n  useEffect(() => {\n    const parseUrlParams = () => {\n      const urlSearchParams = new URLSearchParams(window.location.search);\n      const params = {\n        image: urlSearchParams.get('image'),\n        client: urlSearchParams.get('client'),\n        size: urlSearchParams.get('size'),\n        type: urlSearchParams.get('type')\n      };\n      console.log('Parsed URL parameters:', params);\n      setUrlParams(params);\n\n      // If image URL is provided, store it but don't set as selected product until after capture\n      if (params.image) {\n        try {\n          // Validate the image URL\n          const imageUrl = new URL(params.image);\n\n          // Store product data but don't set as selected until after capture\n          const productType = params.type || 'watches';\n          const defaultSize = productType === 'bracelets' ? 15 : 42;\n          const actualSize = parseInt(params.size) || defaultSize;\n\n          // Store the product data for later use after capture\n          window.pendingProduct = {\n            name: \"Custom Product\",\n            path: params.image,\n            caseDiameter: productType === 'watches' ? actualSize : null,\n            braceletWidth: productType === 'bracelets' ? actualSize : null,\n            caseThickness: productType === 'watches' ? 12 : null,\n            totalWidth: actualSize,\n            totalHeight: productType === 'watches' ? actualSize * 1.15 : actualSize * 3,\n            dialDiameter: productType === 'watches' ? actualSize * 0.75 : null,\n            type: productType,\n            clientId: params.client\n          };\n          console.log('Product data stored for after capture:', {\n            image: params.image,\n            client: params.client,\n            size: params.size\n          });\n        } catch (error) {\n          console.error('Invalid image URL provided:', params.image);\n          // Show product selection if URL is invalid\n          setShowProductSelection(true);\n        }\n      }\n    };\n    parseUrlParams();\n  }, []);\n\n  // Start analytics session when URL parameters are loaded\n  useEffect(() => {\n    if (urlParams.client && urlParams.image) {\n      console.log('Starting analytics session with params:', urlParams);\n      startAnalyticsSession();\n\n      // Initialize enhanced tracking\n      const cleanup = initializeEnhancedTracking();\n\n      // Mark try-on as initiated\n      setConversionFunnel(prev => ({\n        ...prev,\n        initiatedTryOn: true\n      }));\n\n      // End session when user leaves the page\n      const handleBeforeUnload = () => {\n        endAnalyticsSession('abandoned');\n      };\n      const handleVisibilityChange = () => {\n        if (document.visibilityState === 'hidden') {\n          endAnalyticsSession('abandoned');\n        }\n      };\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n      return () => {\n        cleanup();\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        document.removeEventListener('visibilitychange', handleVisibilityChange);\n        endAnalyticsSession('abandoned');\n      };\n    } else {\n      console.log('Missing required parameters for analytics:', {\n        hasClient: !!urlParams.client,\n        hasImage: !!urlParams.image\n      });\n    }\n  }, [urlParams]);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkDevice = () => {\n      const isMobileDevice = window.innerWidth <= 768;\n      setIsMobile(isMobileDevice);\n      setIsDesktop(!isMobileDevice);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n    checkDevice();\n    setVH();\n    window.addEventListener('resize', () => {\n      checkDevice();\n      setVH();\n    });\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Initialize camera when component mounts\n  useEffect(() => {\n    if (typeof window !== 'undefined' && navigator.mediaDevices) {\n      initCamera();\n    }\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Universal wrist size configuration\n  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view\n  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width\n  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width\n  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling\n\n  // Default wrist sizes by gender (top view width in mm)\n  const DEFAULT_WRIST_SIZES = {\n    men: 50 // mm - average men's wrist width from top view\n  };\n\n  // Add wrist size adjustment constant\n  const WRIST_SIZE_OFFSET = 10; // mm - subtract this from input wrist size for correct fitting\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // Minimum adjusted wrist size before scaling stops\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Modify calculateWatchDimensions to use adjusted wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Adjust the wrist size by subtracting the offset, but don't go below MIN_ADJUSTED_WRIST_SIZE\n    const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n\n    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch\n    const inverseWristSizeRatio = defaultWristSize / adjustedWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using adjusted wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / adjustedWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = watchWidthSvg / SVG_VIEWBOX_WIDTH * 100;\n    const watchHeightPercent = watchHeightSvg / SVG_VIEWBOX_HEIGHT * 100;\n    const dialDiameterPercent = dialDiameterSvg / SVG_VIEWBOX_WIDTH * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8),\n      // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10),\n      // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio,\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio: inverseWristSizeRatio,\n      adjustedWristSize\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Calculate scale to match SVG shape height\n    const svgHeight = 300; // Height of the wrist/forearm area in SVG\n    const watchHeight = watchData.totalHeight;\n    const scaleToFitHeight = svgHeight / watchHeight;\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY,\n      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height\n    };\n  };\n\n  // No static product data - products come from URL parameters or client integration\n\n  // Enhanced tracking functions\n  const initializeEnhancedTracking = () => {\n    // Mouse movement tracking\n    let mouseDistance = 0;\n    let lastMousePos = {\n      x: 0,\n      y: 0\n    };\n    const handleMouseMove = e => {\n      const distance = Math.sqrt(Math.pow(e.clientX - lastMousePos.x, 2) + Math.pow(e.clientY - lastMousePos.y, 2));\n      mouseDistance += distance;\n      lastMousePos = {\n        x: e.clientX,\n        y: e.clientY\n      };\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        mouseMovements: mouseDistance\n      }));\n    };\n\n    // Scroll depth tracking\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercent = Math.round(scrollTop / docHeight * 100);\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        scrollDepth: Math.max(prev.scrollDepth, scrollPercent)\n      }));\n      trackInteraction('scroll', {\n        depth: scrollPercent,\n        position: scrollTop\n      });\n    };\n\n    // Focus/blur tracking for attention metrics\n    let focusStartTime = Date.now();\n    let isPageFocused = true;\n    const handleFocus = () => {\n      if (!isPageFocused) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            returnEvents: prev.attentionMetrics.returnEvents + 1\n          }\n        }));\n        focusStartTime = Date.now();\n        isPageFocused = true;\n      }\n    };\n    const handleBlur = () => {\n      if (isPageFocused) {\n        const focusTime = Date.now() - focusStartTime;\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            focusTime: prev.attentionMetrics.focusTime + focusTime,\n            blurEvents: prev.attentionMetrics.blurEvents + 1\n          }\n        }));\n        isPageFocused = false;\n      }\n    };\n\n    // Exit intent detection\n    const handleMouseLeave = e => {\n      if (e.clientY <= 0) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          exitIntent: {\n            detected: true,\n            timestamp: new Date(),\n            beforeConversion: !conversionFunnel.completedPurchase\n          }\n        }));\n        trackInteraction('exit_intent', {\n          timestamp: new Date()\n        });\n      }\n    };\n\n    // Performance monitoring\n    const monitorPerformance = () => {\n      if ('memory' in performance) {\n        const memory = performance.memory;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          memoryUsage: {\n            used: memory.usedJSHeapSize,\n            total: memory.totalJSHeapSize\n          }\n        }));\n      }\n\n      // Network information\n      if ('connection' in navigator) {\n        const connection = navigator.connection;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          networkMetrics: {\n            connectionType: connection.type,\n            downlink: connection.downlink,\n            rtt: connection.rtt,\n            effectiveType: connection.effectiveType\n          }\n        }));\n      }\n    };\n\n    // Add event listeners\n    document.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('focus', handleFocus);\n    window.addEventListener('blur', handleBlur);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    // Start performance monitoring\n    monitorPerformance();\n    const performanceInterval = setInterval(monitorPerformance, 5000);\n\n    // Cleanup function\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('focus', handleFocus);\n      window.removeEventListener('blur', handleBlur);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n      clearInterval(performanceInterval);\n    };\n  };\n\n  // Get user's IP address\n  const getUserIP = async () => {\n    try {\n      const response = await fetch('https://api.ipify.org?format=json');\n      const data = await response.json();\n      return data.ip;\n    } catch (error) {\n      console.warn('Could not get user IP:', error);\n      return null;\n    }\n  };\n\n  // Analytics tracking functions\n  const startAnalyticsSession = async () => {\n    if (!urlParams.client || !urlParams.image) {\n      console.warn('No client ID or image provided for analytics');\n      return;\n    }\n    try {\n      const pageLoadStartTime = performance.now();\n      const userIP = await getUserIP();\n      const sessionData = {\n        clientId: urlParams.client,\n        productId: urlParams.image,\n        productName: (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.name) || 'Custom Product',\n        productCategory: urlParams.type || 'watches',\n        device: {\n          type: window.innerWidth <= 768 ? 'mobile' : window.innerWidth <= 1024 ? 'tablet' : 'desktop',\n          screenResolution: `${window.screen.width}x${window.screen.height}`,\n          os: navigator.platform,\n          browser: navigator.userAgent.split(' ').pop(),\n          userAgent: navigator.userAgent\n        },\n        location: {\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n          referrer: document.referrer,\n          userIP: userIP\n        },\n        behaviorMetrics: {\n          timeToFirstInteraction: null,\n          cameraInitSuccess: false,\n          handDetectionSuccess: false,\n          backgroundRemovalSuccess: false,\n          productSwitches: 0,\n          productViewTimes: [{\n            productId: urlParams.image,\n            duration: 0\n          }],\n          ...behaviorMetrics\n        },\n        performanceMetrics: {\n          pageLoadTime: null,\n          apiResponseTimes: [],\n          errors: [],\n          ...performanceMetrics\n        },\n        qualityMetrics,\n        conversionFunnel\n      };\n      console.log('Sending analytics session data:', sessionData);\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session`;\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(sessionData)\n      });\n      if (response.ok) {\n        const result = await response.json();\n        setSessionId(result.sessionId);\n        setSessionStartTime(new Date());\n\n        // Record page load time\n        const pageLoadTime = performance.now() - pageLoadStartTime;\n        trackPerformanceMetric('pageLoadTime', pageLoadTime);\n        console.log('Analytics session started:', result.sessionId);\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to start analytics session:', errorData);\n        trackError('session_start', errorData.message);\n      }\n    } catch (error) {\n      console.error('Failed to start analytics session:', error);\n      trackError('session_start', error.message);\n    }\n  };\n  const trackInteraction = (type, data = {}) => {\n    const interaction = {\n      type,\n      timestamp: new Date(),\n      data\n    };\n\n    // Update behavior metrics based on interaction type\n    if (type === 'camera_init') {\n      updateBehaviorMetric('cameraInitSuccess', true);\n    } else if (type === 'hand_detection') {\n      updateBehaviorMetric('handDetectionSuccess', true);\n    } else if (type === 'background_removal') {\n      updateBehaviorMetric('backgroundRemovalSuccess', true);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('productSwitches', prev => prev + 1);\n    }\n\n    // Enhanced interaction data\n    interaction.position = data.position || {\n      x: 0,\n      y: 0\n    };\n    interaction.element = data.element || '';\n    interaction.duration = data.duration || 0;\n    interaction.intensity = data.intensity || 0;\n    interaction.sequence = interactions.length + 1;\n    interaction.context = data.context || '';\n\n    // Update feature usage metrics using the new updateBehaviorMetric function\n    if (type === 'screenshot') {\n      updateBehaviorMetric('featureUsage.screenshot', prev => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        completedTryOn: true\n      }));\n    } else if (type === 'share') {\n      updateBehaviorMetric('featureUsage.share', prev => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        sharedResult: true\n      }));\n    } else if (type === 'zoom') {\n      updateBehaviorMetric('featureUsage.zoom', prev => prev + 1);\n    } else if (type === 'size_adjustment') {\n      updateBehaviorMetric('featureUsage.sizeAdjustment', prev => prev + 1);\n    } else if (type === 'color_change') {\n      updateBehaviorMetric('featureUsage.colorChange', prev => prev + 1);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('featureUsage.productRotation', prev => prev + 1);\n    } else if (type === 'capture') {\n      updateBehaviorMetric('featureUsage.cameraToggle', prev => prev + 1);\n    }\n\n    // Track time to first interaction if not set\n    if (!behaviorMetrics.timeToFirstInteraction && sessionStartTime) {\n      const timeToFirstInteraction = (new Date() - sessionStartTime) / 1000;\n      updateBehaviorMetric('timeToFirstInteraction', timeToFirstInteraction);\n    }\n\n    // Calculate engagement score\n    const engagementScore = Math.min(100, interactions.length * 5 + behaviorMetrics.featureUsage.screenshot * 10 + behaviorMetrics.featureUsage.share * 15 + behaviorMetrics.scrollDepth * 0.2 + behaviorMetrics.mouseMovements * 0.001);\n    setBehaviorMetrics(prev => ({\n      ...prev,\n      engagementScore: Math.round(engagementScore)\n    }));\n    setInteractions(prev => [...prev, interaction]);\n    console.log('Enhanced interaction tracked:', interaction);\n  };\n  const trackPerformanceMetric = (metric, value) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/performance`;\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        metric,\n        value,\n        timestamp: new Date()\n      })\n    }).catch(error => {\n      console.error('Failed to track performance metric:', error);\n    });\n  };\n  const trackError = (type, message) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/error`;\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        type,\n        message,\n        timestamp: new Date()\n      })\n    }).catch(error => {\n      console.error('Failed to track error:', error);\n    });\n  };\n  const updateBehaviorMetric = (metric, value) => {\n    // Update local state\n    setBehaviorMetrics(prev => {\n      const newMetrics = {\n        ...prev\n      };\n\n      // Handle nested properties like 'featureUsage.screenshot'\n      if (metric.includes('.')) {\n        const [parent, child] = metric.split('.');\n        if (newMetrics[parent]) {\n          newMetrics[parent] = {\n            ...newMetrics[parent],\n            [child]: value\n          };\n        }\n      } else {\n        newMetrics[metric] = typeof value === 'function' ? value(prev[metric]) : value;\n      }\n      return newMetrics;\n    });\n\n    // Send to backend if session exists\n    if (sessionId) {\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/behavior`;\n      fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          metric,\n          value: typeof value === 'function' ? value(behaviorMetrics[metric] || 0) : value,\n          timestamp: new Date()\n        })\n      }).catch(error => {\n        console.error('Failed to update behavior metric:', error);\n      });\n    }\n  };\n  const endAnalyticsSession = async (outcome = 'abandoned') => {\n    if (!sessionId) return;\n    try {\n      const endTime = new Date();\n      const duration = sessionStartTime ? Math.floor((endTime - sessionStartTime) / 1000) : 0;\n      const updateData = {\n        endTime,\n        duration,\n        outcome,\n        interactions,\n        behaviorMetrics,\n        performanceMetrics,\n        qualityMetrics,\n        conversionFunnel\n      };\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}`;\n      const response = await fetch(apiUrl, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updateData)\n      });\n      if (response.ok) {\n        console.log('Analytics session ended:', outcome, 'Duration:', duration, 'seconds');\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to end analytics session:', errorData);\n      }\n    } catch (error) {\n      console.error('Failed to end analytics session:', error);\n    }\n  };\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n    let displayWidth, displayHeight, offsetX, offsetY;\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, (320 / 800 * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, (150 / 600 * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, 160 / 800 * displayWidth * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, 300 / 600 * displayHeight * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, (340 / 800 * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, (240 / 600 * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, 120 / 800 * displayWidth * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, 120 / 600 * displayHeight * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n      return wristInPosition && handInPosition;\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: (watchData === null || watchData === void 0 ? void 0 : watchData.dialSize) || 40,\n        // Default to 40mm if not found\n        dimensions: watchData // Pass full watch dimensions for scaling\n      });\n    }, 50);\n  };\n\n  // Screenshot functionality with tracking\n  const takeScreenshot = () => {\n    if (!capturedImageRef.current) return;\n    try {\n      // Create a canvas to combine the captured image with the product overlay\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n\n      // Set canvas size\n      canvas.width = capturedImageRef.current.naturalWidth || 800;\n      canvas.height = capturedImageRef.current.naturalHeight || 600;\n\n      // Draw the captured image\n      ctx.drawImage(capturedImageRef.current, 0, 0, canvas.width, canvas.height);\n\n      // Convert to blob and download\n      canvas.toBlob(blob => {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `tryon-${Date.now()}.png`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n\n        // Track screenshot\n        trackInteraction('screenshot', {\n          timestamp: new Date(),\n          productId: urlParams.image,\n          sessionDuration: sessionStartTime ? (new Date() - sessionStartTime) / 1000 : 0\n        });\n\n        // Mark as completed try-on\n        setConversionFunnel(prev => ({\n          ...prev,\n          completedTryOn: true\n        }));\n      }, 'image/png');\n    } catch (error) {\n      console.error('Screenshot failed:', error);\n      trackError('screenshot_failed', error.message);\n    }\n  };\n\n  // Zoom functionality with tracking\n  const handleZoom = zoomLevel => {\n    trackInteraction('zoom', {\n      zoomLevel,\n      timestamp: new Date(),\n      element: 'product_overlay'\n    });\n  };\n\n  // Share functionality with tracking\n  const handleShare = async () => {\n    try {\n      if (navigator.share) {\n        await navigator.share({\n          title: 'Check out my virtual try-on!',\n          text: 'I tried on this product virtually',\n          url: window.location.href\n        });\n        trackInteraction('share', {\n          method: 'native_share',\n          timestamp: new Date()\n        });\n        setConversionFunnel(prev => ({\n          ...prev,\n          sharedResult: true\n        }));\n      } else {\n        // Fallback to copying URL\n        await navigator.clipboard.writeText(window.location.href);\n        alert('Link copied to clipboard!');\n        trackInteraction('share', {\n          method: 'copy_link',\n          timestamp: new Date()\n        });\n      }\n    } catch (error) {\n      console.error('Share failed:', error);\n      trackError('share_failed', error.message);\n    }\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCameraReady) return;\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n      setShowHandGuide(false);\n\n      // Track capture interaction\n      trackInteraction('capture', {\n        method: 'manual_capture',\n        hasProduct: !!urlParams.image,\n        timestamp: new Date()\n      });\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current && isCameraReady) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = gender => {\n    setUserGender('men'); // Single gender role\n    setUserWristSize(50); // Set initial size for men\n    trackInteraction('size_adjustment', {\n      type: 'gender_change',\n      gender: 'men',\n      newWristSize: 50\n    });\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = size => {\n    setUserWristSize(size);\n    trackInteraction('size_adjustment', {\n      type: 'wrist_size_change',\n      size\n    });\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserWristSize(50); // Default to men's size\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Handle product selection\n  const handleProductSelect = product => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Add touch gesture handlers\n  const handleTouchStart = e => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n  const handleTouchMove = e => {\n    if (!isDragging) return;\n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n\n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    setIsDragging(false);\n\n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n\n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = e => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Desktop QR Code Component\n  const DesktopQRCode = () => {\n    // Generate dynamic QR code URL based on URL parameters or default\n    const generateQRValue = () => {\n      const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';\n      const baseUrl = `${websiteUrl}/tryon`;\n\n      // If we have URL parameters, include them in the QR code\n      if (urlParams.image || urlParams.client || urlParams.size || urlParams.type) {\n        const params = new URLSearchParams();\n        if (urlParams.image) params.append('image', urlParams.image);\n        if (urlParams.client) params.append('client', urlParams.client);\n        if (urlParams.size) params.append('size', urlParams.size);\n        if (urlParams.type) params.append('type', urlParams.type);\n        return `${baseUrl}?${params.toString()}`;\n      }\n\n      // Default QR code for general try-on\n      return baseUrl;\n    };\n    const qrValue = generateQRValue();\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.desktopContainer,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.qrContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: styles.qrTitle,\n          children: \"Scan QR Code to Try On\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1704,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.qrSubtitle,\n          children: urlParams.image ? \"Scan to try on this specific product on your mobile device\" : \"Open this page on your mobile device to experience the virtual try-on feature\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.qrWrapper,\n          children: /*#__PURE__*/_jsxDEV(QRCodeSVG, {\n            value: qrValue,\n            size: 256,\n            level: \"H\",\n            bgColor: \"#FFFFFF\",\n            fgColor: \"#2D8C88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1712,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1711,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.qrLink,\n          children: qrValue\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1720,\n          columnNumber: 11\n        }, this), urlParams.client && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.clientInfo,\n          children: [\"Client: \", urlParams.client]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1722,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.homeBtn,\n          onClick: onBackToHome,\n          \"aria-label\": \"Home\",\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1724,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1703,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1702,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Return desktop view if not on mobile\n  if (isDesktop) {\n    return /*#__PURE__*/_jsxDEV(DesktopQRCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1738,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Show intro popup before try-on UI\n  if (showIntroPopup) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100vw',\n        height: '100vh',\n        background: 'rgba(0,0,0,0.55)',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: 20,\n          padding: '32px 24px',\n          maxWidth: 340,\n          width: '90vw',\n          boxShadow: '0 8px 32px rgba(0,0,0,0.18)',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: '#2D8C88',\n            fontWeight: 700,\n            fontSize: 22,\n            marginBottom: 16\n          },\n          children: \"Welcome to Virtual Try-On\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1765,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#333',\n            fontSize: 16,\n            marginBottom: 12\n          },\n          children: \"Please align your wrist and hand within the guide lines for the most accurate try-on experience.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1766,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#666',\n            fontSize: 14,\n            marginBottom: 18\n          },\n          children: \"The product image is for illustration only. Actual size and fit may vary.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1769,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            background: '#2D8C88',\n            color: 'white',\n            border: 'none',\n            borderRadius: 12,\n            padding: '12px 32px',\n            fontWeight: 600,\n            fontSize: 16,\n            cursor: 'pointer',\n            boxShadow: '0 2px 8px rgba(45,140,136,0.12)'\n          },\n          onClick: () => setShowIntroPopup(false),\n          \"aria-label\": \"OK, start try-on\",\n          children: \"OK, Start Try-On\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1772,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1756,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1744,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Update product selection panel JSX\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.cameraContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        style: styles.cameraFeed,\n        autoPlay: true,\n        playsInline: true,\n        muted: true,\n        onPlaying: () => setIsCameraReady(true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1798,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1806,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: capturedImageRef,\n        style: styles.capturedImage,\n        alt: \"Captured hand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1807,\n        columnNumber: 9\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.homeBtn,\n        onClick: () => window.history.back(),\n        \"aria-label\": \"Back\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1815,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: isMobile ? 10 : 20,\n          right: isMobile ? 10 : 20,\n          zIndex: 20,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: '8px',\n          padding: '12px',\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: '20px',\n          backdropFilter: 'blur(10px)',\n          WebkitBackdropFilter: 'blur(10px)',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n          border: '1px solid rgba(255, 255, 255, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch-label\",\n          children: \"Auto Capture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1843,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"switch-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: isAutoCaptureEnabled,\n            onChange: handleAutoCaptureToggle,\n            disabled: isCountdownActive,\n            \"aria-label\": \"Toggle auto capture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1847,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"switch-slider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1854,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1846,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1826,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.backBtn,\n        onClick: handleBackWithReset,\n        \"aria-label\": \"Back\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1861,\n        columnNumber: 11\n      }, this), isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.countdownDisplay,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownNumber,\n          children: countdown\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1873,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.countdownText,\n          children: \"Auto capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1874,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1872,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusText,\n          children: \"Position your arm and wrist in the guide area\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1881,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.statusSubtext,\n          children: \"Countdown will start automatically when detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1882,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1880,\n        columnNumber: 11\n      }, this), isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.statusMessage,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.statusText,\n            backgroundColor: 'rgba(45, 140, 136, 0.9)'\n          },\n          children: \"Perfect! Starting countdown...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1888,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1887,\n        columnNumber: 11\n      }, this), !isCaptured && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '8%',\n          left: '50%',\n          transform: 'translateX(-50%)',\n          background: 'rgba(0,0,0,0.55)',\n          color: 'white',\n          padding: '10px 22px',\n          borderRadius: 16,\n          fontSize: 15,\n          fontWeight: 600,\n          zIndex: 12,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.18)',\n          textAlign: 'center',\n          letterSpacing: 0.1,\n          maxWidth: 320\n        },\n        children: \"Align your wrist and hand within the white guide lines for best results.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1896,\n        columnNumber: 11\n      }, this), showHandGuide && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.handGuide,\n          opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n          filter: isAutoCaptureEnabled && isHandInPosition ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))' : isAutoCaptureEnabled && !isHandInPosition ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))' : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n        },\n        className: isMobile ? 'mobile-hand-guide' : '',\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          viewBox: \"0 0 800 600\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1934,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"5\",\n            fill: \"none\",\n            strokeLinecap: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1947,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"320\",\n            y: \"150\",\n            width: \"160\",\n            height: \"300\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\",\n            rx: \"15\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1961,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"400\",\n            cy: \"300\",\n            r: \"60\",\n            fill: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            opacity: isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\",\n            stroke: isAutoCaptureEnabled && isHandInPosition ? \"#2D8C88\" : isAutoCaptureEnabled && !isHandInPosition ? \"#ff6b6b\" : \"white\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1985,\n            columnNumber: 15\n          }, this), isAutoCaptureEnabled && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"140\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"WRIST & FOREARM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2009,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"480\",\n              textAnchor: \"middle\",\n              fill: \"white\",\n              fontSize: \"14\",\n              fontWeight: \"bold\",\n              children: \"HAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2012,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1932,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1919,\n        columnNumber: 11\n      }, this), isCaptured && selectedProduct && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '12%',\n            left: '50%',\n            transform: 'translateX(-50%)',\n            background: 'rgba(255,255,255,0.92)',\n            color: '#2D8C88',\n            fontWeight: 600,\n            fontSize: 15,\n            borderRadius: 12,\n            padding: '8px 18px',\n            zIndex: 20,\n            boxShadow: '0 2px 8px rgba(45,140,136,0.10)',\n            textAlign: 'center',\n            maxWidth: 320\n          },\n          children: \"Product image is for illustration only. Actual size and fit may vary.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2025,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.productPosition,\n            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n            height: activeTab === 'Watches' ? (() => {\n              const defaultWristSize = DEFAULT_WRIST_SIZE;\n              const isLargeWrist = userWristSize >= DEFAULT_WRIST_SIZE;\n              if (isLargeWrist) {\n                // For large wrists, increase height by 40% to allow exceeding SVG height\n                const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;\n                return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;\n              }\n              return `${WATCH_HEIGHT}%`;\n            })() : `${BRACELET_HEIGHT}%`,\n            // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)\n            clipPath: (() => {\n              const isLargeWrist = userWristSize >= DEFAULT_WRIST_SIZE;\n              return activeTab === 'Watches' && isLargeWrist ? 'ellipse(220px 60px at 50% 50%)' : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE ? 'ellipse(220px 60px at 50% 50%)' : 'none';\n            })(),\n            overflow: 'hidden'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct,\n              alt: \"Selected product\",\n              style: {\n                width: '100%',\n                height: '100%',\n                objectFit: 'contain',\n                transform: activeTab === 'Bracelets' ? (() => {\n                  // Use exact bracelet fitting logic from tryon.js with enhanced vertical flip\n                  const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 30})`;\n\n                  // Apply realistic bracelet positioning based on hand detection\n                  // Bracelets need vertical flipping to appear correctly on the wrist\n                  if (isRightHand) {\n                    // For right hand: flip horizontally (like tryon.js) and add vertical flip for realism\n                    return `${baseTransform} scaleX(-1) scaleY(-1)`;\n                  } else {\n                    // For left hand: only vertical flip for proper bracelet orientation\n                    return `${baseTransform} scaleY(-1)`;\n                  }\n                })() : (_selectedProduct$dime => {\n                  const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n                  const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n                  const isLargeWrist = userGender === 'men' && adjustedWristSize >= 50;\n                  if (isLargeWrist) {\n                    // For larger wrists, apply height scaling increase and allow exceeding SVG height\n                    const sizeIncrease = (adjustedWristSize - defaultWristSize) / defaultWristSize;\n                    const heightScale = 1 + sizeIncrease * 0.4; // 40% height increase\n                    const widthScale = defaultWristSize / adjustedWristSize; // Decrease width as wrist increases\n\n                    return `scale(${WATCH_HEIGHT / 25 * widthScale}) scaleX(${widthScale}) scaleY(${heightScale})`;\n                  }\n\n                  // For smaller wrists, use the original working logic with SVG height constraint\n                  return `scale(${Math.min(WATCH_HEIGHT / 25 * (adjustedWristSize > defaultWristSize ? defaultWristSize / adjustedWristSize : defaultWristSize / adjustedWristSize), 300 / (((_selectedProduct$dime = selectedProduct.dimensions) === null || _selectedProduct$dime === void 0 ? void 0 : _selectedProduct$dime.totalHeight) || 47) // Scale to match SVG height\n                  )}) scaleX(${adjustedWristSize > defaultWristSize ? defaultWristSize / adjustedWristSize : 1})`;\n                })(),\n                filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n              },\n              onLoad: e => removeBackground(e.target, urlParams.type === 'bracelets' || activeTab === 'Bracelets' ? 'bracelet' : 'watch')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2078,\n              columnNumber: 17\n            }, this), (urlParams.type !== 'bracelets' && activeTab === 'Watches' || !urlParams.type && activeTab === 'Watches') && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                bottom: '-30px',\n                left: '50%',\n                transform: 'translateX(-50%)',\n                fontSize: '11px',\n                fontWeight: '600',\n                color: 'white',\n                backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                padding: '3px 8px',\n                borderRadius: '12px',\n                whiteSpace: 'nowrap',\n                pointerEvents: 'none',\n                boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                zIndex: 2\n              },\n              children: [selectedProduct.dialSize || selectedProduct.caseDiameter || urlParams.size || '42', \"mm\", userWristSize !== DEFAULT_WRIST_SIZE && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '10px',\n                  opacity: 0.8,\n                  marginLeft: '4px'\n                },\n                children: (() => {\n                  const wristSizeRatio = DEFAULT_WRIST_SIZE / userWristSize;\n                  let scalingPercentage;\n                  if (userWristSize < DEFAULT_WRIST_SIZE) {\n                    // Realistic scaling for smaller wrists\n                    const sizeDifference = DEFAULT_WRIST_SIZE - userWristSize;\n                    const maxSizeDifference = DEFAULT_WRIST_SIZE * 0.25;\n                    const clampedDifference = Math.min(sizeDifference, maxSizeDifference);\n                    const moderateScaleFactor = 1 + clampedDifference / DEFAULT_WRIST_SIZE * 0.6;\n                    scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);\n                  } else {\n                    // Standard scaling for larger wrists\n                    scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);\n                  }\n\n                  // Add debug indicator for large wrists\n                  const debugSuffix = userWristSize >= DEFAULT_WRIST_SIZE ? ' 🔥' : '';\n                  return `(${userWristSize < DEFAULT_WRIST_SIZE ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2147,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2129,\n              columnNumber: 19\n            }, this), (urlParams.type === 'bracelets' || activeTab === 'Bracelets') && typeof selectedProduct === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                bottom: '-30px',\n                left: '50%',\n                transform: 'translateX(-50%)',\n                fontSize: '11px',\n                fontWeight: '600',\n                color: 'white',\n                backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                padding: '3px 8px',\n                borderRadius: '12px',\n                whiteSpace: 'nowrap',\n                pointerEvents: 'none',\n                boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                zIndex: 2\n              },\n              children: [selectedProduct.braceletWidth || urlParams.size || '15', \"mm width\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2177,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2070,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2043,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), !isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.captureBtn,\n        className: isMobile ? 'mobile-capture-btn' : '',\n        onClick: handleCapture,\n        \"aria-label\": isCaptured ? \"Select Products\" : \"Capture\",\n        disabled: !isCameraReady,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.captureInner,\n          className: isMobile ? 'mobile-inner-circle' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2210,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2203,\n        columnNumber: 11\n      }, this), !isCameraReady && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          background: 'rgba(0,0,0,0.7)',\n          color: 'white',\n          padding: '18px 32px',\n          borderRadius: 18,\n          fontSize: 18,\n          fontWeight: 600,\n          zIndex: 100\n        },\n        children: \"Loading camera...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2215,\n        columnNumber: 11\n      }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.resetBtn,\n        onClick: () => window.location.reload(),\n        \"aria-label\": \"Reset\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"white\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2240,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2239,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2234,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1797,\n      columnNumber: 7\n    }, this), isCaptured && /*#__PURE__*/_jsxDEV(\"button\", {\n      style: styles.wristSizeFloatingBtn,\n      className: isMobile ? 'mobile-btn' : '',\n      onClick: () => setShowWristSizeModal(true),\n      \"aria-label\": \"Adjust wrist size\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2255,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2254,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: styles.wristSizeText,\n        children: [userWristSize, \"mm\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2257,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2248,\n      columnNumber: 9\n    }, this), showWristSizeModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modalOverlay,\n      onClick: () => setShowWristSizeModal(false),\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.wristSizeModal,\n        onClick: e => e.stopPropagation(),\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.modalTitle,\n            children: \"Adjust Wrist Size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modalCloseBtn,\n            onClick: () => setShowWristSizeModal(false),\n            \"aria-label\": \"Close\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2281,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2275,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2273,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.modalContent,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.genderSelection,\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                ...styles.genderButton,\n                ...styles.genderButtonActive\n              },\n              onClick: () => handleGenderChange('men'),\n              children: \"Standard (50mm)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2289,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.sliderContainer,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.sliderLabel,\n              children: [\"Wrist Size: \", userWristSize, \"mm\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: 40,\n              max: 65,\n              value: userWristSize,\n              onChange: e => handleWristSizeChange(parseInt(e.target.value)),\n              style: styles.slider\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.sliderLabels,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"40mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"65mm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.presetButtons,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(DEFAULT_WRIST_SIZE),\n                children: [\"Average (\", DEFAULT_WRIST_SIZE, \"mm)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(40),\n                children: \"Small (40mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.presetButton,\n                onClick: () => handleWristSizeChange(65),\n                children: \"Large (65mm)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2332,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2319,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2301,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2286,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2268,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2263,\n      columnNumber: 9\n    }, this), showProductSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: panelRef,\n      style: {\n        ...styles.productSelection,\n        transform: `translateY(${panelPosition}px)`,\n        touchAction: 'none'\n      },\n      className: isMobile ? 'mobile-product-panel' : '',\n      \"aria-modal\": \"true\",\n      role: \"dialog\",\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dragHandle,\n        \"aria-hidden\": \"true\",\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2361,\n        columnNumber: 11\n      }, this), !urlParams.type && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productTabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Watches' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Watches'),\n          children: \"Watches\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2371,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.tab,\n            ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n          },\n          onClick: () => handleTabChange('Bracelets'),\n          children: \"Bracelets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2380,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2370,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.productScroll,\n        className: \"product-scroll\",\n        children: getCurrentProducts().length > 0 ? getCurrentProducts().map((product, index) => {\n          // Simple null check only\n          if (!product) return null;\n          const isSelected = (typeof selectedProduct === 'object' ? selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.path : selectedProduct) === product.path;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              ...styles.productItem,\n              borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n              backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n            },\n            title: `${product.name} - ${product.caseDiameter || 'N/A'}mm`,\n            onClick: () => handleProductSelect(product),\n            \"aria-label\": `Select ${product.name} ${product.caseDiameter || 'N/A'}mm`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.path,\n              alt: product.name,\n              style: styles.productImage,\n              onError: e => {\n                e.target.parentElement.style.display = 'none';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2411,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.productLabel,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productName,\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2420,\n                columnNumber: 23\n              }, this), activeTab === 'Watches' && product.caseDiameter && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.productSize,\n                children: [product.caseDiameter, \"mm\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2422,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2419,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2400,\n            columnNumber: 19\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.noProductsMessage,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsIcon,\n            children: \"\\uD83D\\uDCF1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2430,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsTitle,\n            children: \"No Products Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2431,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsText,\n            children: \"This try-on experience is designed to be accessed through client websites with specific product parameters.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2432,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.noProductsSubtext,\n            children: \"Please visit a client's product page and click the \\\"Try On Virtually\\\" button.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2435,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2429,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2391,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2347,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1796,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\n_s(Tryon, \"UgzuLHhdUIStSZjClZEOkTgTq1k=\");\n_c = Tryon;\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\n  productPosition: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 8,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '25vw',\n    // width controlled\n    aspectRatio: '1 / 1.6',\n    // maintain height-to-width ratio (adjust as needed)\n    minWidth: '100px',\n    minHeight: '160px',\n    // fallback for unsupported aspect-ratio\n    pointerEvents: 'none'\n  },\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  desktopContainer: {\n    position: 'relative',\n    height: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: '20px'\n  },\n  qrContainer: {\n    backgroundColor: 'white',\n    padding: '40px',\n    borderRadius: '24px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n    textAlign: 'center',\n    maxWidth: '500px',\n    width: '100%'\n  },\n  qrTitle: {\n    fontSize: '28px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '16px'\n  },\n  qrSubtitle: {\n    fontSize: '16px',\n    color: '#666',\n    marginBottom: '32px',\n    lineHeight: '1.5'\n  },\n  qrWrapper: {\n    backgroundColor: 'white',\n    padding: '20px',\n    borderRadius: '16px',\n    display: 'inline-block',\n    marginBottom: '24px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'\n  },\n  qrLink: {\n    fontSize: '14px',\n    color: '#2D8C88',\n    marginBottom: '32px',\n    wordBreak: 'break-all'\n  },\n  clientInfo: {\n    fontSize: '12px',\n    color: '#666',\n    marginBottom: '16px',\n    fontStyle: 'italic'\n  },\n  noProductsMessage: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '40px 20px',\n    textAlign: 'center',\n    height: '200px'\n  },\n  noProductsIcon: {\n    fontSize: '48px',\n    marginBottom: '16px'\n  },\n  noProductsTitle: {\n    fontSize: '18px',\n    fontWeight: '600',\n    color: '#333',\n    marginBottom: '12px'\n  },\n  noProductsText: {\n    fontSize: '14px',\n    color: '#666',\n    lineHeight: '1.5',\n    marginBottom: '8px',\n    maxWidth: '280px'\n  },\n  noProductsSubtext: {\n    fontSize: '12px',\n    color: '#999',\n    lineHeight: '1.4',\n    maxWidth: '260px'\n  }\n};\nexport default Tryon;\nvar _c;\n$RefreshReg$(_c, \"Tryon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "QRCodeSVG", "uShapeCutter", "removeProductBackground", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sliderCSS", "document", "getElementById", "styleElement", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onBackToHome", "_s", "videoRef", "capturedImageRef", "canvasRef", "isCaptured", "setIsCaptured", "selectedProduct", "setSelectedProduct", "isRightHand", "setIsRightHand", "showProductSelection", "setShowProductSelection", "activeTab", "setActiveTab", "showHandGuide", "setShowHandGuide", "isMobile", "setIsMobile", "isDesktop", "setIsDesktop", "urlParams", "setUrlParams", "image", "client", "size", "type", "sessionId", "setSessionId", "sessionStartTime", "setSessionStartTime", "interactions", "setInteractions", "behaviorMetrics", "setBehaviorMetrics", "timeToFirstInteraction", "productSwitches", "backgroundRemovalSuccess", "handDetectionSuccess", "totalInteractions", "cameraInitSuccess", "engagementScore", "scrollDepth", "mouseMovements", "hoverEvents", "gestureEvents", "featureUsage", "cameraToggle", "productRotation", "sizeAdjustment", "colorChange", "screenshot", "share", "zoom", "exitIntent", "detected", "timestamp", "beforeConversion", "attentionMetrics", "focusTime", "blurEvents", "returnEvents", "idleTime", "performanceMetrics", "setPerformanceMetrics", "frameRate", "average", "min", "max", "drops", "memoryUsage", "used", "total", "networkMetrics", "renderMetrics", "resourceLoadTimes", "qualityMetrics", "setQualityMetrics", "handDetectionAccuracy", "backgroundRemovalQuality", "productFitAccuracy", "userSatisfactionScore", "technicalIssues", "conversionFunnel", "setConversionFunnel", "viewedProduct", "initiatedTryOn", "completedTryOn", "sharedResult", "addedToCart", "proceededToCheckout", "completedPurchase", "userGender", "setUserGender", "userWristSize", "setUserWristSize", "showWristSizeModal", "setShowWristSizeModal", "isAutoCaptureEnabled", "setIsAutoCaptureEnabled", "countdown", "setCountdown", "isHandInPosition", "setIsHandInPosition", "isCountdownActive", "setIsCountdownActive", "panelPosition", "setPanelPosition", "isDragging", "setIsDragging", "startY", "setStartY", "panelRef", "showIntroPopup", "setShowIntroPopup", "watches", "name", "path", "caseDiameter", "caseThickness", "totalWidth", "totalHeight", "dialDiameter", "dialSize", "bracelets", "removeBackground", "imgElement", "productType", "processedImageUrl", "src", "console", "log", "error", "warn", "basicBackgroundRemoval", "finalProcessedUrl", "style", "filter", "mixBlendMode", "opacity", "uShapedImage", "canvas", "ctx", "getContext", "width", "naturalWidth", "height", "naturalHeight", "drawImage", "imageData", "getImageData", "data", "i", "length", "r", "g", "b", "threshold", "Math", "abs", "putImageData", "toDataURL", "detectHandOrientation", "random", "initCamera", "setIsCameraReady", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "ideal", "current", "srcObject", "err", "display", "isCameraReady", "<PERSON><PERSON><PERSON><PERSON>", "videoWidth", "videoHeight", "parseUrlParams", "urlSearchParams", "URLSearchParams", "window", "location", "search", "params", "get", "imageUrl", "URL", "defaultSize", "actualSize", "parseInt", "pendingProduct", "<PERSON><PERSON><PERSON><PERSON>", "clientId", "startAnalyticsSession", "cleanup", "initializeEnhancedTracking", "prev", "handleBeforeUnload", "endAnalyticsSession", "handleVisibilityChange", "visibilityState", "addEventListener", "removeEventListener", "hasClient", "hasImage", "checkDevice", "isMobileDevice", "innerWidth", "setVH", "vh", "innerHeight", "documentElement", "setProperty", "setTimeout", "DEFAULT_WRIST_SIZE", "MIN_WRIST_SIZE", "MAX_WRIST_SIZE", "ASSUMED_DIAL_SIZE", "DEFAULT_WRIST_SIZES", "men", "WRIST_SIZE_OFFSET", "MIN_ADJUSTED_WRIST_SIZE", "SVG_WRIST_CIRCLE_DIAMETER", "SVG_VIEWBOX_WIDTH", "SVG_VIEWBOX_HEIGHT", "WATCH_WIDTH", "BRACELET_WIDTH", "WATCH_HEIGHT", "BRACELET_HEIGHT", "calculateWatchDimensions", "watch", "containerWidth", "containerHeight", "defaultWristSize", "adjustedWristSize", "inverseWristSizeRatio", "mmToSvgScale", "watchWidthSvg", "watchHeightSvg", "dialDiameterSvg", "watchWidthPercent", "watchHeightPercent", "dialDiameterPercent", "positionX", "positionY", "scale", "realWidth", "realHeight", "wristSizeRatio", "getWatchPosition", "watchData", "baseDimensions", "adjustedX", "adjustedY", "svgHeight", "watchHeight", "scaleToFitHeight", "mouseDistance", "lastMousePos", "x", "y", "handleMouseMove", "e", "distance", "sqrt", "pow", "clientX", "clientY", "handleScroll", "scrollTop", "pageYOffset", "doc<PERSON><PERSON>ght", "scrollHeight", "scrollPercent", "round", "trackInteraction", "depth", "position", "focusStartTime", "Date", "now", "isPageFocused", "handleFocus", "handleBlur", "handleMouseLeave", "monitorPerformance", "performance", "memory", "usedJSHeapSize", "totalJSHeapSize", "connection", "connectionType", "downlink", "rtt", "effectiveType", "performanceInterval", "setInterval", "clearInterval", "getUserIP", "response", "fetch", "json", "ip", "pageLoadStartTime", "userIP", "sessionData", "productId", "productName", "productCategory", "device", "screenResolution", "screen", "os", "platform", "browser", "userAgent", "split", "pop", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "referrer", "productViewTimes", "duration", "pageLoadTime", "apiResponseTimes", "errors", "baseUrl", "process", "env", "REACT_APP_API_URL", "origin", "apiUrl", "replace", "method", "headers", "body", "JSON", "stringify", "ok", "result", "trackPerformanceMetric", "errorData", "trackError", "message", "interaction", "updateBehaviorMetric", "element", "intensity", "sequence", "context", "metric", "value", "catch", "newMetrics", "includes", "parent", "child", "outcome", "endTime", "floor", "updateData", "detectHandInPosition", "videoContainer", "parentElement", "containerRect", "getBoundingClientRect", "videoAspect", "containerAspect", "displayWidth", "displayHeight", "offsetX", "offsetY", "scaleX", "scaleY", "rectX", "rectY", "rectWidth", "rectHeight", "circleX", "circleY", "circleWidth", "circleHeight", "rectImageData", "rectData", "circleImageData", "circleData", "rectSkinPixels", "rectTotalPixels", "circleSkinPixels", "circleTotalPixels", "isSkinTone", "condition1", "condition2", "condition3", "condition4", "rectSkinRatio", "circleSkinRatio", "wristInPosition", "handInPosition", "applyProductToWatchPosition", "productPath", "find", "w", "dimensions", "takeScreenshot", "toBlob", "blob", "url", "createObjectURL", "a", "href", "download", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "sessionDuration", "handleZoom", "zoomLevel", "handleShare", "title", "text", "clipboard", "writeText", "alert", "handleCapture", "capturedDataUrl", "hasProduct", "handleBack", "handleGenderChange", "gender", "newWristSize", "handleWristSizeChange", "handleTabChange", "tabName", "handleAutoCaptureToggle", "newState", "handleBackWithReset", "getCurrentProducts", "handleProductSelect", "product", "interval", "countdownInterval", "handleTouchStart", "touches", "handleTouchMove", "currentY", "diff", "handleTouchEnd", "handleClickOutside", "target", "closest", "DesktopQRCode", "generateQRValue", "websiteUrl", "REACT_APP_WEBSITE_URL", "append", "toString", "qrValue", "styles", "desktopContainer", "children", "qr<PERSON><PERSON><PERSON>", "qrTitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "qrSubtitle", "qrWrapper", "level", "bgColor", "fgColor", "qrLink", "clientInfo", "homeBtn", "onClick", "top", "left", "background", "zIndex", "alignItems", "justifyContent", "borderRadius", "padding", "max<PERSON><PERSON><PERSON>", "boxShadow", "textAlign", "color", "fontWeight", "fontSize", "marginBottom", "border", "cursor", "container", "cameraContainer", "ref", "cameraFeed", "autoPlay", "playsInline", "muted", "onPlaying", "capturedImage", "alt", "history", "back", "right", "flexDirection", "gap", "backgroundColor", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "className", "checked", "onChange", "disabled", "backBtn", "countdownDisplay", "countdownNumber", "countdownText", "statusMessage", "statusText", "statusSubtext", "transform", "letterSpacing", "handGuide", "viewBox", "xmlns", "d", "stroke", "strokeWidth", "fill", "strokeLinecap", "rx", "cx", "cy", "textAnchor", "productPosition", "isLargeWrist", "sizeIncrease", "clipPath", "overflow", "objectFit", "baseTransform", "_selectedProduct$dime", "heightScale", "widthScale", "onLoad", "bottom", "whiteSpace", "pointerEvents", "marginLeft", "scalingPercentage", "sizeDifference", "maxSizeDifference", "clampedDifference", "moderateScaleFactor", "toFixed", "debugSuffix", "captureBtn", "captureInner", "resetBtn", "reload", "wristSizeFloatingBtn", "wristSizeText", "modalOverlay", "wristSizeModal", "stopPropagation", "modalHeader", "modalTitle", "modalCloseBtn", "modalContent", "genderSelection", "genderButton", "genderButtonActive", "slide<PERSON><PERSON><PERSON><PERSON>", "slider<PERSON><PERSON><PERSON>", "slider", "slider<PERSON><PERSON><PERSON>", "presetButtons", "presetButton", "productSelection", "touchAction", "role", "onTouchStart", "onTouchMove", "onTouchEnd", "dragHandle", "productTabs", "tab", "productScroll", "map", "index", "isSelected", "productItem", "borderColor", "productImage", "onError", "productLabel", "productSize", "noProductsMessage", "noProductsIcon", "noProductsTitle", "noProductsText", "noProductsSubtext", "_c", "fontFamily", "WebkitTapHighlightColor", "WebkitOverflowScrolling", "flex", "WebkitTransform", "transition", "outline", "switchContainer", "switchTrack", "switchButton", "margin", "switchLabel", "textShadow", "marginTop", "animation", "WebkitFilter", "aspectRatio", "min<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "borderBottom", "overflowY", "wristSizeContent", "wristSizeTitle", "wristSizeSubtitle", "lineHeight", "sizeChange", "WebkitAppearance", "appearance", "continueButton", "borderTopLeftRadius", "borderTopRightRadius", "<PERSON><PERSON><PERSON><PERSON>", "userSelect", "WebkitUserSelect", "closeBtn", "gridTemplateColumns", "paddingBottom", "scrollbarWidth", "scrollbarColor", "textOverflow", "wordBreak", "fontStyle", "$RefreshReg$"], "sources": ["D:/Via/test/viatryon/src/pages/Tryon.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { QRCodeSVG } from 'qrcode.react'; // Fix the import\nimport uShape<PERSON>utter from '../utils/uShapeCutter';\nimport { removeProductBackground } from '../utils/backgroundRemover';\n\n// Add CSS for range slider styling\nconst sliderCSS = `\n  input[type=\"range\"]::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: #2D8C88;\n    cursor: pointer;\n    border: none;\n    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);\n  }\n\n  input[type=\"range\"]::-webkit-slider-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n  }\n\n  input[type=\"range\"]::-moz-range-track {\n    background: #e0e0e0;\n    height: 6px;\n    border-radius: 3px;\n    border: none;\n  }\n\n  input[type=\"range\"]:focus {\n    outline: none;\n  }\n\n  input[type=\"range\"]:focus::-webkit-slider-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  input[type=\"range\"]:focus::-moz-range-thumb {\n    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);\n  }\n\n  /* Switch styles */\n  .switch-container {\n    position: relative;\n    display: inline-block;\n    width: 60px;\n    height: 34px;\n  }\n\n  .switch-container input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n\n  .switch-slider {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.2);\n    transition: .4s;\n    border-radius: 34px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n  }\n\n  .switch-slider:before {\n    position: absolute;\n    content: \"\";\n    height: 26px;\n    width: 26px;\n    left: 2px;\n    bottom: 2px;\n    background-color: white;\n    transition: .4s;\n    border-radius: 50%;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  }\n\n  input:checked + .switch-slider {\n    background-color: #2D8C88;\n  }\n\n  input:checked + .switch-slider:before {\n    transform: translateX(26px);\n  }\n\n  input:disabled + .switch-slider {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n  .switch-label {\n    font-size: 12px;\n    font-weight: 700;\n    color: white;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);\n    margin-bottom: 8px;\n    letter-spacing: 0.5px;\n  }\n`;\n\n// Inject CSS (only once)\nif (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {\n  const styleElement = document.createElement('style');\n  styleElement.id = 'wrist-size-slider-styles';\n  styleElement.textContent = sliderCSS;\n  document.head.appendChild(styleElement);\n}\n\nconst Tryon = ({ onBackToHome }) => {\n  // Refs for DOM elements\n  const videoRef = useRef(null);\n  const capturedImageRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // State variables\n  const [isCaptured, setIsCaptured] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [isRightHand, setIsRightHand] = useState(false);\n  const [showProductSelection, setShowProductSelection] = useState(false);\n  const [activeTab, setActiveTab] = useState('Watches');\n  const [showHandGuide, setShowHandGuide] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [isDesktop, setIsDesktop] = useState(false);\n\n  // URL parameters state\n  const [urlParams, setUrlParams] = useState({\n    image: null,\n    client: null,\n    size: null,\n    type: null\n  });\n\n  // Analytics tracking state\n  const [sessionId, setSessionId] = useState(null);\n  const [sessionStartTime, setSessionStartTime] = useState(null);\n  const [interactions, setInteractions] = useState([]);\n  const [behaviorMetrics, setBehaviorMetrics] = useState({\n    timeToFirstInteraction: null,\n    productSwitches: 0,\n    backgroundRemovalSuccess: false,\n    handDetectionSuccess: false,\n    totalInteractions: 0,\n    cameraInitSuccess: false,\n    engagementScore: 0,\n    scrollDepth: 0,\n    mouseMovements: 0,\n    hoverEvents: [],\n    gestureEvents: [],\n    featureUsage: {\n      cameraToggle: 0,\n      productRotation: 0,\n      sizeAdjustment: 0,\n      colorChange: 0,\n      screenshot: 0,\n      share: 0,\n      zoom: 0\n    },\n    exitIntent: {\n      detected: false,\n      timestamp: null,\n      beforeConversion: false\n    },\n    attentionMetrics: {\n      focusTime: 0,\n      blurEvents: 0,\n      returnEvents: 0,\n      idleTime: 0\n    }\n  });\n\n  // Enhanced tracking state\n  const [performanceMetrics, setPerformanceMetrics] = useState({\n    frameRate: { average: 0, min: 0, max: 0, drops: 0 },\n    memoryUsage: { used: 0, total: 0 },\n    networkMetrics: {},\n    renderMetrics: {},\n    resourceLoadTimes: []\n  });\n  const [qualityMetrics, setQualityMetrics] = useState({\n    handDetectionAccuracy: 0,\n    backgroundRemovalQuality: 0,\n    productFitAccuracy: 0,\n    userSatisfactionScore: 0,\n    technicalIssues: []\n  });\n  const [conversionFunnel, setConversionFunnel] = useState({\n    viewedProduct: true,\n    initiatedTryOn: false,\n    completedTryOn: false,\n    sharedResult: false,\n    addedToCart: false,\n    proceededToCheckout: false,\n    completedPurchase: false\n  });\n\n  // Watch size customization state\n  const [userGender, setUserGender] = useState('men'); // Single gender role\n  const [userWristSize, setUserWristSize] = useState(50); // Default wrist size in mm\n  const [showWristSizeModal, setShowWristSizeModal] = useState(false); // Mobile-friendly modal\n\n  // Autocapture state variables\n  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [isHandInPosition, setIsHandInPosition] = useState(false);\n  const [isCountdownActive, setIsCountdownActive] = useState(false);\n\n  // Add new state for panel position\n  const [panelPosition, setPanelPosition] = useState(0);\n  const [isDragging, setIsDragging] = useState(false);\n  const [startY, setStartY] = useState(0);\n  const panelRef = useRef(null);\n\n  // Intro popup state\n  const [showIntroPopup, setShowIntroPopup] = useState(true);\n\n  // Realistic watch data with actual dimensions (in millimeters)\n  const watches = [\n    {\n      name: \"Classic Black\",\n      path: \"watches/watch_1.png\",\n      // Rolex Submariner style - 40mm case\n      caseDiameter: 41, // mm\n      caseThickness: 12.5, // mm\n      totalWidth: 42, // mm (including crown)\n      totalHeight: 47, // mm (lug to lug)\n      dialDiameter: 31, // mm (visible dial)\n      type: \"dress\",\n      dialSize: 40\n    },\n    {\n      name: \"Silver Chrono\",\n      path: \"watches/watch_2.png\",\n      // Omega Speedmaster style - 42mm case\n      caseDiameter: 42, // mm\n      caseThickness: 13.2, // mm\n      totalWidth: 44, // mm\n      totalHeight: 48.5, // mm\n      dialDiameter: 33, // mm\n      type: \"sport\",\n      dialSize: 42\n    },\n    {\n      name: \"Gold Luxury\",\n      path: \"watches/watch_3.png\",\n      // Patek Philippe Calatrava style - 38mm case\n      caseDiameter: 39, // mm\n      caseThickness: 8.5, // mm\n      totalWidth: 39, // mm\n      totalHeight: 45, // mm\n      dialDiameter: 30, // mm\n      type: \"luxury\",\n      dialSize: 38\n    },\n    {\n      name: \"Sport Blue\",\n      path: \"watches/watch_6.png\",\n      // Apple Watch style - 44mm case\n      caseDiameter: 41, // mm (width)\n      caseThickness: 10.7, // mm\n      totalWidth: 44, // mm\n      totalHeight: 38, // mm (height - rectangular)\n      dialDiameter: 35, // mm (screen diagonal)\n      type: \"smartwatch\",\n      dialSize: 44\n    },\n    {\n      name: \"Minimalist\",\n      path: \"watches/watch_5.png\",\n      // Daniel Wellington style - 36mm case\n      caseDiameter: 36, // mm\n      caseThickness: 6, // mm\n      totalWidth: 37, // mm\n      totalHeight: 43, // mm\n      dialDiameter: 28, // mm\n      type: \"minimalist\",\n      dialSize: 36\n    },\n    {\n      name: \"Rose Gold\",\n      path: \"watches/watch_4.png\",\n      // Michael Kors style - 39mm case\n      caseDiameter: 44, // mm\n      caseThickness: 11, // mm\n      totalWidth: 41, // mm\n      totalHeight: 46, // mm\n      dialDiameter: 31, // mm\n      type: \"fashion\",\n      dialSize: 41\n    }\n  ];\n\n  const bracelets = [\n    { name: \"Silver Chain\", path: \"bracelets/bracelet_1.png\" },\n    { name: \"Gold Bangle\", path: \"bracelets/bracelet_2.png\" },\n    { name: \"Leather Wrap\", path: \"bracelets/bracelet_3.png\" },\n    { name: \"Diamond Tennis\", path: \"bracelets/bracelet_4.png\" },\n    { name: \"Beaded Stone\", path: \"bracelets/bracelet_5.png\" },\n    { name: \"Charm Bracelet\", path: \"bracelets/bracelet_6.png\" }\n  ];\n\n  // Enhanced background removal function with proper bracelet processing sequence\n  const removeBackground = async (imgElement, productType = 'watch') => {\n    try {\n      let processedImageUrl = imgElement.src;\n\n      // For bracelets: Apply U-shape cutter → Background removal → Vertical flip\n      if (productType === 'bracelets') {\n        try {\n          // Step 1: Apply U-shape cutter first\n          processedImageUrl = await uShapeCutter(imgElement.src);\n          console.log('U-shape cutting applied successfully for bracelet');\n\n          // Step 2: Remove background after U-shape cutting\n          processedImageUrl = await removeProductBackground(processedImageUrl, productType);\n          console.log('Background removal applied after U-shape cutting');\n\n          // Step 3: Apply vertical flip based on hand detection (will be handled in transform)\n          imgElement.src = processedImageUrl;\n\n        } catch (error) {\n          console.warn('Bracelet processing sequence failed:', error);\n          // Fallback to basic processing\n          await basicBackgroundRemoval(imgElement, productType);\n        }\n      } else {\n        // For watches: Just apply background removal\n        const finalProcessedUrl = await removeProductBackground(processedImageUrl, productType);\n        imgElement.src = finalProcessedUrl;\n      }\n\n      // Apply product-specific styling that preserves all colors\n      imgElement.style.filter = 'none';\n      imgElement.style.mixBlendMode = 'normal';\n      imgElement.style.opacity = '1';\n\n      console.log(`Background removal completed for ${productType}`);\n\n    } catch (error) {\n      console.warn('Enhanced background removal failed:', error);\n      // Fallback to basic background removal\n      await basicBackgroundRemoval(imgElement, productType);\n    }\n  };\n\n  // Fallback basic background removal\n  const basicBackgroundRemoval = async (imgElement, productType) => {\n    try {\n      console.log(`Applying fallback background removal for ${productType}`);\n\n      if (productType === 'bracelet') {\n        // Apply U-shape cutter for bracelets\n        const uShapedImage = await uShapeCutter(imgElement.src);\n        imgElement.src = uShapedImage;\n      }\n\n      // Basic background removal with conservative settings\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      canvas.width = imgElement.naturalWidth;\n      canvas.height = imgElement.naturalHeight;\n      ctx.drawImage(imgElement, 0, 0);\n\n      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n      const data = imageData.data;\n\n      // Process each pixel - only remove very pure white backgrounds\n      for (let i = 0; i < data.length; i += 4) {\n        const r = data[i];\n        const g = data[i + 1];\n        const b = data[i + 2];\n\n        // Only remove very white pixels (more conservative for watches)\n        const threshold = productType === 'watch' ? 252 : 248;\n        if (r > threshold && g > threshold && b > threshold &&\n            Math.abs(r - g) < 3 && Math.abs(g - b) < 3) {\n          data[i + 3] = 0; // Make transparent\n        }\n      }\n\n      ctx.putImageData(imageData, 0, 0);\n      imgElement.src = canvas.toDataURL('image/png');\n\n      console.log(`Fallback background removal completed for ${productType}`);\n    } catch (error) {\n      console.error('Basic background removal failed:', error);\n    }\n  };\n\n  // Hand detection logic from tryon.js\n  const detectHandOrientation = (imageData) => {\n    // Simple heuristic for demo purposes - can be enhanced with ML models\n    return Math.random() > 0.5;\n  };\n\n  // Initialize camera\n  const initCamera = async () => {\n    setIsCameraReady(false);\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1920 },\n          height: { ideal: 1080 }\n        }\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n    } catch (err) {\n      console.error(\"Error accessing camera:\", err);\n      // Demo mode fallback\n      if (capturedImageRef.current) {\n        capturedImageRef.current.src = \"sample-hand.jpg\";\n        capturedImageRef.current.style.display = \"block\";\n      }\n      console.log(\"Camera not available - demo mode\");\n      setIsCaptured(true);\n      setShowProductSelection(true);\n    }\n  };\n\n  // Add camera ready state\n  const [isCameraReady, setIsCameraReady] = useState(false);\n\n  // Capture current frame\n  const captureFrame = () => {\n    if (!videoRef.current || !canvasRef.current || !isCameraReady) return null;\n    const video = videoRef.current;\n    if (!video.videoWidth || !video.videoHeight) return null;\n    const canvas = canvasRef.current;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n    return canvas.toDataURL('image/png');\n  };\n\n\n\n  // Parse URL parameters on component mount\n  useEffect(() => {\n    const parseUrlParams = () => {\n      const urlSearchParams = new URLSearchParams(window.location.search);\n      const params = {\n        image: urlSearchParams.get('image'),\n        client: urlSearchParams.get('client'),\n        size: urlSearchParams.get('size'),\n        type: urlSearchParams.get('type')\n      };\n\n      console.log('Parsed URL parameters:', params);\n      setUrlParams(params);\n\n      // If image URL is provided, store it but don't set as selected product until after capture\n      if (params.image) {\n        try {\n          // Validate the image URL\n          const imageUrl = new URL(params.image);\n\n          // Store product data but don't set as selected until after capture\n          const productType = params.type || 'watches';\n          const defaultSize = productType === 'bracelets' ? 15 : 42;\n          const actualSize = parseInt(params.size) || defaultSize;\n\n          // Store the product data for later use after capture\n          window.pendingProduct = {\n            name: \"Custom Product\",\n            path: params.image,\n            caseDiameter: productType === 'watches' ? actualSize : null,\n            braceletWidth: productType === 'bracelets' ? actualSize : null,\n            caseThickness: productType === 'watches' ? 12 : null,\n            totalWidth: actualSize,\n            totalHeight: productType === 'watches' ? actualSize * 1.15 : actualSize * 3,\n            dialDiameter: productType === 'watches' ? actualSize * 0.75 : null,\n            type: productType,\n            clientId: params.client\n          };\n\n          console.log('Product data stored for after capture:', {\n            image: params.image,\n            client: params.client,\n            size: params.size\n          });\n        } catch (error) {\n          console.error('Invalid image URL provided:', params.image);\n          // Show product selection if URL is invalid\n          setShowProductSelection(true);\n        }\n      }\n    };\n\n    parseUrlParams();\n  }, []);\n\n  // Start analytics session when URL parameters are loaded\n  useEffect(() => {\n    if (urlParams.client && urlParams.image) {\n      console.log('Starting analytics session with params:', urlParams);\n      startAnalyticsSession();\n\n      // Initialize enhanced tracking\n      const cleanup = initializeEnhancedTracking();\n\n      // Mark try-on as initiated\n      setConversionFunnel(prev => ({\n        ...prev,\n        initiatedTryOn: true\n      }));\n\n      // End session when user leaves the page\n      const handleBeforeUnload = () => {\n        endAnalyticsSession('abandoned');\n      };\n\n      const handleVisibilityChange = () => {\n        if (document.visibilityState === 'hidden') {\n          endAnalyticsSession('abandoned');\n        }\n      };\n\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      return () => {\n        cleanup();\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        document.removeEventListener('visibilitychange', handleVisibilityChange);\n        endAnalyticsSession('abandoned');\n      };\n    } else {\n      console.log('Missing required parameters for analytics:', {\n        hasClient: !!urlParams.client,\n        hasImage: !!urlParams.image\n      });\n    }\n  }, [urlParams]);\n\n  // Detect mobile device and set viewport height for Chrome mobile\n  useEffect(() => {\n    const checkDevice = () => {\n      const isMobileDevice = window.innerWidth <= 768;\n      setIsMobile(isMobileDevice);\n      setIsDesktop(!isMobileDevice);\n    };\n\n    // Fix viewport height for Chrome mobile\n    const setVH = () => {\n      const vh = window.innerHeight * 0.01;\n      document.documentElement.style.setProperty('--vh', `${vh}px`);\n    };\n\n    checkDevice();\n    setVH();\n\n    window.addEventListener('resize', () => {\n      checkDevice();\n      setVH();\n    });\n\n    window.addEventListener('orientationchange', () => {\n      setTimeout(() => {\n        setVH();\n      }, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', setVH);\n    };\n  }, []);\n\n  // Initialize camera when component mounts\n  useEffect(() => {\n    if (typeof window !== 'undefined' && navigator.mediaDevices) {\n      initCamera();\n    }\n  }, []);\n\n  // Realistic sizing configuration\n  // Average adult wrist circumference: 165mm (men), 155mm (women)\n  // Average wrist width when viewed from top: ~55-65mm\n  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)\n  // This means the circle represents approximately 60mm in real life\n\n  // Universal wrist size configuration\n  const DEFAULT_WRIST_SIZE = 50; // mm - ideal wrist width from top view\n  const MIN_WRIST_SIZE = 35; // mm - minimum wrist width\n  const MAX_WRIST_SIZE = 65; // mm - maximum wrist width\n  const ASSUMED_DIAL_SIZE = 42; // mm - assumed real dial size for initial scaling\n\n  // Default wrist sizes by gender (top view width in mm)\n  const DEFAULT_WRIST_SIZES = {\n    men: 50    // mm - average men's wrist width from top view\n  };\n\n  // Add wrist size adjustment constant\n  const WRIST_SIZE_OFFSET = 10; // mm - subtract this from input wrist size for correct fitting\n  const MIN_ADJUSTED_WRIST_SIZE = 37; // Minimum adjusted wrist size before scaling stops\n\n  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units\n  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width\n  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height\n\n  // Size configuration - Increased for better visibility\n  const WATCH_WIDTH = 25; // percentage of container width\n  const BRACELET_WIDTH = 15; // percentage of container width\n  const WATCH_HEIGHT = 38; // percentage of container height\n  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility\n\n  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch\n  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch\n  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet\n  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet\n  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale\n\n  // Modify calculateWatchDimensions to use adjusted wrist size\n  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {\n    // Get the default wrist size for the current gender\n    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n\n    // Adjust the wrist size by subtracting the offset, but don't go below MIN_ADJUSTED_WRIST_SIZE\n    const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n\n    // Calculate INVERSE scaling factor - smaller wrist = larger watch, larger wrist = smaller watch\n    const inverseWristSizeRatio = defaultWristSize / adjustedWristSize;\n\n    // Calculate the scale factor from real world to SVG coordinates using adjusted wrist size\n    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / adjustedWristSize;\n\n    // Convert watch dimensions to SVG units\n    const watchWidthSvg = watch.totalWidth * mmToSvgScale;\n    const watchHeightSvg = watch.totalHeight * mmToSvgScale;\n    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;\n\n    // Convert SVG units to container percentages\n    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;\n    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;\n    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;\n\n    // Calculate positioning - watches should be positioned on the wrist circle\n    const positionX = 50; // Center horizontally (400/800 = 50%)\n    const positionY = 50; // Center vertically (300/600 = 50%)\n\n    return {\n      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility\n      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility\n      dialDiameter: dialDiameterPercent,\n      positionX,\n      positionY,\n      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * inverseWristSizeRatio,\n      realWidth: watch.totalWidth,\n      realHeight: watch.totalHeight,\n      caseDiameter: watch.caseDiameter,\n      wristSizeRatio: inverseWristSizeRatio,\n      adjustedWristSize\n    };\n  };\n\n  // Calculate watch position based on hand orientation and anatomy\n  const getWatchPosition = (watchData, isRightHand) => {\n    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);\n\n    // Adjust position based on hand orientation\n    // Watches are typically worn on the top of the wrist\n    let adjustedX = baseDimensions.positionX;\n    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist\n\n    // For right hand, watch might be positioned slightly differently\n    if (isRightHand) {\n      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand\n    }\n\n    // Calculate scale to match SVG shape height\n    const svgHeight = 300; // Height of the wrist/forearm area in SVG\n    const watchHeight = watchData.totalHeight;\n    const scaleToFitHeight = svgHeight / watchHeight;\n\n    // Adjust for different watch types\n    switch (watchData.type) {\n      case 'smartwatch':\n        // Smart watches are often worn higher on the wrist\n        adjustedY -= 1;\n        break;\n      case 'luxury':\n        // Luxury watches might be positioned more precisely\n        adjustedY -= 0.5;\n        break;\n      case 'sport':\n        // Sport watches might be worn slightly looser\n        adjustedY += 0.5;\n        break;\n      default:\n        break;\n    }\n\n    return {\n      ...baseDimensions,\n      positionX: adjustedX,\n      positionY: adjustedY,\n      scale: Math.min(baseDimensions.scale, scaleToFitHeight) // Ensure watch doesn't exceed SVG height\n    };\n  };\n\n  // No static product data - products come from URL parameters or client integration\n\n  // Enhanced tracking functions\n  const initializeEnhancedTracking = () => {\n    // Mouse movement tracking\n    let mouseDistance = 0;\n    let lastMousePos = { x: 0, y: 0 };\n\n    const handleMouseMove = (e) => {\n      const distance = Math.sqrt(\n        Math.pow(e.clientX - lastMousePos.x, 2) +\n        Math.pow(e.clientY - lastMousePos.y, 2)\n      );\n      mouseDistance += distance;\n      lastMousePos = { x: e.clientX, y: e.clientY };\n\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        mouseMovements: mouseDistance\n      }));\n    };\n\n    // Scroll depth tracking\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercent = Math.round((scrollTop / docHeight) * 100);\n\n      setBehaviorMetrics(prev => ({\n        ...prev,\n        scrollDepth: Math.max(prev.scrollDepth, scrollPercent)\n      }));\n\n      trackInteraction('scroll', { depth: scrollPercent, position: scrollTop });\n    };\n\n    // Focus/blur tracking for attention metrics\n    let focusStartTime = Date.now();\n    let isPageFocused = true;\n\n    const handleFocus = () => {\n      if (!isPageFocused) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            returnEvents: prev.attentionMetrics.returnEvents + 1\n          }\n        }));\n        focusStartTime = Date.now();\n        isPageFocused = true;\n      }\n    };\n\n    const handleBlur = () => {\n      if (isPageFocused) {\n        const focusTime = Date.now() - focusStartTime;\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          attentionMetrics: {\n            ...prev.attentionMetrics,\n            focusTime: prev.attentionMetrics.focusTime + focusTime,\n            blurEvents: prev.attentionMetrics.blurEvents + 1\n          }\n        }));\n        isPageFocused = false;\n      }\n    };\n\n    // Exit intent detection\n    const handleMouseLeave = (e) => {\n      if (e.clientY <= 0) {\n        setBehaviorMetrics(prev => ({\n          ...prev,\n          exitIntent: {\n            detected: true,\n            timestamp: new Date(),\n            beforeConversion: !conversionFunnel.completedPurchase\n          }\n        }));\n        trackInteraction('exit_intent', { timestamp: new Date() });\n      }\n    };\n\n    // Performance monitoring\n    const monitorPerformance = () => {\n      if ('memory' in performance) {\n        const memory = performance.memory;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          memoryUsage: {\n            used: memory.usedJSHeapSize,\n            total: memory.totalJSHeapSize\n          }\n        }));\n      }\n\n      // Network information\n      if ('connection' in navigator) {\n        const connection = navigator.connection;\n        setPerformanceMetrics(prev => ({\n          ...prev,\n          networkMetrics: {\n            connectionType: connection.type,\n            downlink: connection.downlink,\n            rtt: connection.rtt,\n            effectiveType: connection.effectiveType\n          }\n        }));\n      }\n    };\n\n    // Add event listeners\n    document.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('focus', handleFocus);\n    window.addEventListener('blur', handleBlur);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    // Start performance monitoring\n    monitorPerformance();\n    const performanceInterval = setInterval(monitorPerformance, 5000);\n\n    // Cleanup function\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('focus', handleFocus);\n      window.removeEventListener('blur', handleBlur);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n      clearInterval(performanceInterval);\n    };\n  };\n\n  // Get user's IP address\n  const getUserIP = async () => {\n    try {\n      const response = await fetch('https://api.ipify.org?format=json');\n      const data = await response.json();\n      return data.ip;\n    } catch (error) {\n      console.warn('Could not get user IP:', error);\n      return null;\n    }\n  };\n\n  // Analytics tracking functions\n  const startAnalyticsSession = async () => {\n    if (!urlParams.client || !urlParams.image) {\n      console.warn('No client ID or image provided for analytics');\n      return;\n    }\n\n    try {\n      const pageLoadStartTime = performance.now();\n      const userIP = await getUserIP();\n\n      const sessionData = {\n        clientId: urlParams.client,\n        productId: urlParams.image,\n        productName: selectedProduct?.name || 'Custom Product',\n        productCategory: urlParams.type || 'watches',\n        device: {\n          type: window.innerWidth <= 768 ? 'mobile' : window.innerWidth <= 1024 ? 'tablet' : 'desktop',\n          screenResolution: `${window.screen.width}x${window.screen.height}`,\n          os: navigator.platform,\n          browser: navigator.userAgent.split(' ').pop(),\n          userAgent: navigator.userAgent\n        },\n        location: {\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n          referrer: document.referrer,\n          userIP: userIP\n        },\n        behaviorMetrics: {\n          timeToFirstInteraction: null,\n          cameraInitSuccess: false,\n          handDetectionSuccess: false,\n          backgroundRemovalSuccess: false,\n          productSwitches: 0,\n          productViewTimes: [{\n            productId: urlParams.image,\n            duration: 0\n          }],\n          ...behaviorMetrics\n        },\n        performanceMetrics: {\n          pageLoadTime: null,\n          apiResponseTimes: [],\n          errors: [],\n          ...performanceMetrics\n        },\n        qualityMetrics,\n        conversionFunnel\n      };\n\n      console.log('Sending analytics session data:', sessionData);\n\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session`;\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(sessionData),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setSessionId(result.sessionId);\n        setSessionStartTime(new Date());\n        \n        // Record page load time\n        const pageLoadTime = performance.now() - pageLoadStartTime;\n        trackPerformanceMetric('pageLoadTime', pageLoadTime);\n        \n        console.log('Analytics session started:', result.sessionId);\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to start analytics session:', errorData);\n        trackError('session_start', errorData.message);\n      }\n    } catch (error) {\n      console.error('Failed to start analytics session:', error);\n      trackError('session_start', error.message);\n    }\n  };\n\n  const trackInteraction = (type, data = {}) => {\n    const interaction = {\n      type,\n      timestamp: new Date(),\n      data\n    };\n\n    // Update behavior metrics based on interaction type\n    if (type === 'camera_init') {\n      updateBehaviorMetric('cameraInitSuccess', true);\n    } else if (type === 'hand_detection') {\n      updateBehaviorMetric('handDetectionSuccess', true);\n    } else if (type === 'background_removal') {\n      updateBehaviorMetric('backgroundRemovalSuccess', true);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('productSwitches', (prev) => prev + 1);\n    }\n\n    // Enhanced interaction data\n    interaction.position = data.position || { x: 0, y: 0 };\n    interaction.element = data.element || '';\n    interaction.duration = data.duration || 0;\n    interaction.intensity = data.intensity || 0;\n    interaction.sequence = interactions.length + 1;\n    interaction.context = data.context || '';\n\n    // Update feature usage metrics using the new updateBehaviorMetric function\n    if (type === 'screenshot') {\n      updateBehaviorMetric('featureUsage.screenshot', (prev) => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        completedTryOn: true\n      }));\n    } else if (type === 'share') {\n      updateBehaviorMetric('featureUsage.share', (prev) => prev + 1);\n      setConversionFunnel(prev => ({\n        ...prev,\n        sharedResult: true\n      }));\n    } else if (type === 'zoom') {\n      updateBehaviorMetric('featureUsage.zoom', (prev) => prev + 1);\n    } else if (type === 'size_adjustment') {\n      updateBehaviorMetric('featureUsage.sizeAdjustment', (prev) => prev + 1);\n    } else if (type === 'color_change') {\n      updateBehaviorMetric('featureUsage.colorChange', (prev) => prev + 1);\n    } else if (type === 'product_switch') {\n      updateBehaviorMetric('featureUsage.productRotation', (prev) => prev + 1);\n    } else if (type === 'capture') {\n      updateBehaviorMetric('featureUsage.cameraToggle', (prev) => prev + 1);\n    }\n\n    // Track time to first interaction if not set\n    if (!behaviorMetrics.timeToFirstInteraction && sessionStartTime) {\n      const timeToFirstInteraction = (new Date() - sessionStartTime) / 1000;\n      updateBehaviorMetric('timeToFirstInteraction', timeToFirstInteraction);\n    }\n\n    // Calculate engagement score\n    const engagementScore = Math.min(100,\n      (interactions.length * 5) +\n      (behaviorMetrics.featureUsage.screenshot * 10) +\n      (behaviorMetrics.featureUsage.share * 15) +\n      (behaviorMetrics.scrollDepth * 0.2) +\n      (behaviorMetrics.mouseMovements * 0.001)\n    );\n\n    setBehaviorMetrics(prev => ({\n      ...prev,\n      engagementScore: Math.round(engagementScore)\n    }));\n\n    setInteractions(prev => [...prev, interaction]);\n    console.log('Enhanced interaction tracked:', interaction);\n  };\n\n  const trackPerformanceMetric = (metric, value) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/performance`;\n\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        metric,\n        value,\n        timestamp: new Date()\n      }),\n    }).catch(error => {\n      console.error('Failed to track performance metric:', error);\n    });\n  };\n\n  const trackError = (type, message) => {\n    const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n    const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/error`;\n\n    fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        type,\n        message,\n        timestamp: new Date()\n      }),\n    }).catch(error => {\n      console.error('Failed to track error:', error);\n    });\n  };\n\n  const updateBehaviorMetric = (metric, value) => {\n    // Update local state\n    setBehaviorMetrics(prev => {\n      const newMetrics = { ...prev };\n\n      // Handle nested properties like 'featureUsage.screenshot'\n      if (metric.includes('.')) {\n        const [parent, child] = metric.split('.');\n        if (newMetrics[parent]) {\n          newMetrics[parent] = { ...newMetrics[parent], [child]: value };\n        }\n      } else {\n        newMetrics[metric] = typeof value === 'function' ? value(prev[metric]) : value;\n      }\n\n      return newMetrics;\n    });\n\n    // Send to backend if session exists\n    if (sessionId) {\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}/behavior`;\n\n      fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          metric,\n          value: typeof value === 'function' ? value(behaviorMetrics[metric] || 0) : value,\n          timestamp: new Date()\n        }),\n      }).catch(error => {\n        console.error('Failed to update behavior metric:', error);\n      });\n    }\n  };\n\n  const endAnalyticsSession = async (outcome = 'abandoned') => {\n    if (!sessionId) return;\n\n    try {\n      const endTime = new Date();\n      const duration = sessionStartTime ? Math.floor((endTime - sessionStartTime) / 1000) : 0;\n\n      const updateData = {\n        endTime,\n        duration,\n        outcome,\n        interactions,\n        behaviorMetrics,\n        performanceMetrics,\n        qualityMetrics,\n        conversionFunnel\n      };\n\n      const baseUrl = process.env.REACT_APP_API_URL || window.location.origin;\n      const apiUrl = `${baseUrl.replace(/\\/+$/, '')}/api/analytics/session/${sessionId}`;\n\n      const response = await fetch(apiUrl, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (response.ok) {\n        console.log('Analytics session ended:', outcome, 'Duration:', duration, 'seconds');\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to end analytics session:', errorData);\n      }\n    } catch (error) {\n      console.error('Failed to end analytics session:', error);\n    }\n  };\n\n\n\n\n\n  // Detect if arm and wrist are within the SVG guide area\n  const detectHandInPosition = () => {\n    if (!videoRef.current || !canvasRef.current) return false;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // Get the video container dimensions to calculate the guide area\n    const videoContainer = video.parentElement;\n    const containerRect = videoContainer.getBoundingClientRect();\n\n    // Calculate the actual video display area (accounting for object-fit: cover)\n    const videoAspect = video.videoWidth / video.videoHeight;\n    const containerAspect = containerRect.width / containerRect.height;\n\n    let displayWidth, displayHeight, offsetX, offsetY;\n\n    if (videoAspect > containerAspect) {\n      // Video is wider - height fills container, width is cropped\n      displayHeight = containerRect.height;\n      displayWidth = displayHeight * videoAspect;\n      offsetX = (displayWidth - containerRect.width) / 2;\n      offsetY = 0;\n    } else {\n      // Video is taller - width fills container, height is cropped\n      displayWidth = containerRect.width;\n      displayHeight = displayWidth / videoAspect;\n      offsetX = 0;\n      offsetY = (displayHeight - containerRect.height) / 2;\n    }\n\n    // Calculate the guide areas in canvas coordinates\n    // SVG viewBox is 800x600\n    // Main rectangle: x=\"320\" y=\"150\" width=\"160\" height=\"300\" (wrist/forearm area)\n    // Circle: cx=\"400\" cy=\"300\" r=\"60\" (hand area)\n\n    const scaleX = canvas.width / displayWidth;\n    const scaleY = canvas.height / displayHeight;\n\n    // Wrist/forearm area (rectangle)\n    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);\n    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);\n    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);\n    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);\n\n    // Hand area (circle)\n    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds\n    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds\n    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter\n    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter\n\n    try {\n      // Check wrist/forearm area (rectangle)\n      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);\n      const rectData = rectImageData.data;\n\n      // Check hand area (circle approximation)\n      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);\n      const circleData = circleImageData.data;\n\n      let rectSkinPixels = 0;\n      let rectTotalPixels = 0;\n      let circleSkinPixels = 0;\n      let circleTotalPixels = 0;\n\n      // Enhanced skin tone detection\n      const isSkinTone = (r, g, b) => {\n        // Multiple skin tone ranges to cover different skin colors\n        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;\n        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;\n        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;\n        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;\n\n        return condition1 || condition2 || condition3 || condition4;\n      };\n\n      // Analyze rectangle area (wrist/forearm)\n      for (let i = 0; i < rectData.length; i += 4) {\n        const r = rectData[i];\n        const g = rectData[i + 1];\n        const b = rectData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          rectSkinPixels++;\n        }\n        rectTotalPixels++;\n      }\n\n      // Analyze circle area (hand)\n      for (let i = 0; i < circleData.length; i += 4) {\n        const r = circleData[i];\n        const g = circleData[i + 1];\n        const b = circleData[i + 2];\n\n        if (isSkinTone(r, g, b)) {\n          circleSkinPixels++;\n        }\n        circleTotalPixels++;\n      }\n\n      // Calculate skin ratios\n      const rectSkinRatio = rectSkinPixels / rectTotalPixels;\n      const circleSkinRatio = circleSkinPixels / circleTotalPixels;\n\n      // Both wrist/forearm AND hand areas must have sufficient skin tone presence\n      // Wrist area needs at least 20% skin tone (forearm/wrist)\n      // Hand area needs at least 25% skin tone (hand/fingers)\n      const wristInPosition = rectSkinRatio > 0.20;\n      const handInPosition = circleSkinRatio > 0.25;\n\n      return wristInPosition && handInPosition;\n\n    } catch (error) {\n      console.warn('Hand detection error:', error);\n      return false;\n    }\n  };\n\n  // Apply product to watch position with dynamic scaling\n  const applyProductToWatchPosition = (productPath, productType) => {\n    // Clear any existing product first\n    setSelectedProduct(null);\n\n    // Find the watch data for dial size\n    const watchData = watches.find(w => w.path === productPath);\n\n    // Small delay to ensure the old product is removed before adding new one\n    setTimeout(() => {\n      setSelectedProduct({\n        path: productPath,\n        dialSize: watchData?.dialSize || 40, // Default to 40mm if not found\n        dimensions: watchData // Pass full watch dimensions for scaling\n      });\n    }, 50);\n  };\n\n  // Screenshot functionality with tracking\n  const takeScreenshot = () => {\n    if (!capturedImageRef.current) return;\n\n    try {\n      // Create a canvas to combine the captured image with the product overlay\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n\n      // Set canvas size\n      canvas.width = capturedImageRef.current.naturalWidth || 800;\n      canvas.height = capturedImageRef.current.naturalHeight || 600;\n\n      // Draw the captured image\n      ctx.drawImage(capturedImageRef.current, 0, 0, canvas.width, canvas.height);\n\n      // Convert to blob and download\n      canvas.toBlob((blob) => {\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `tryon-${Date.now()}.png`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n\n        // Track screenshot\n        trackInteraction('screenshot', {\n          timestamp: new Date(),\n          productId: urlParams.image,\n          sessionDuration: sessionStartTime ? (new Date() - sessionStartTime) / 1000 : 0\n        });\n\n        // Mark as completed try-on\n        setConversionFunnel(prev => ({\n          ...prev,\n          completedTryOn: true\n        }));\n      }, 'image/png');\n    } catch (error) {\n      console.error('Screenshot failed:', error);\n      trackError('screenshot_failed', error.message);\n    }\n  };\n\n  // Zoom functionality with tracking\n  const handleZoom = (zoomLevel) => {\n    trackInteraction('zoom', {\n      zoomLevel,\n      timestamp: new Date(),\n      element: 'product_overlay'\n    });\n  };\n\n  // Share functionality with tracking\n  const handleShare = async () => {\n    try {\n      if (navigator.share) {\n        await navigator.share({\n          title: 'Check out my virtual try-on!',\n          text: 'I tried on this product virtually',\n          url: window.location.href\n        });\n\n        trackInteraction('share', {\n          method: 'native_share',\n          timestamp: new Date()\n        });\n\n        setConversionFunnel(prev => ({\n          ...prev,\n          sharedResult: true\n        }));\n      } else {\n        // Fallback to copying URL\n        await navigator.clipboard.writeText(window.location.href);\n        alert('Link copied to clipboard!');\n\n        trackInteraction('share', {\n          method: 'copy_link',\n          timestamp: new Date()\n        });\n      }\n    } catch (error) {\n      console.error('Share failed:', error);\n      trackError('share_failed', error.message);\n    }\n  };\n\n  // Handle capture button click\n  const handleCapture = () => {\n    if (!isCameraReady) return;\n    if (!isCaptured) {\n      const capturedDataUrl = captureFrame();\n      if (capturedImageRef.current && capturedDataUrl) {\n        capturedImageRef.current.src = capturedDataUrl;\n        capturedImageRef.current.style.display = 'block';\n      }\n      setIsCaptured(true);\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n      setShowHandGuide(false);\n\n      // Track capture interaction\n      trackInteraction('capture', {\n        method: 'manual_capture',\n        hasProduct: !!urlParams.image,\n        timestamp: new Date()\n      });\n\n      // Detect hand orientation\n      if (canvasRef.current && videoRef.current && isCameraReady) {\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext('2d');\n        canvas.width = videoRef.current.videoWidth;\n        canvas.height = videoRef.current.videoHeight;\n        ctx.drawImage(videoRef.current, 0, 0);\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        setIsRightHand(detectHandOrientation(imageData));\n      }\n    } else {\n      // Only show product selection if no product is provided via URL\n      if (!urlParams.image) {\n        setShowProductSelection(true);\n      }\n    }\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    if (capturedImageRef.current) {\n      capturedImageRef.current.style.display = 'none';\n    }\n    setIsCaptured(false);\n    setSelectedProduct(null);\n    setIsRightHand(false);\n    setShowProductSelection(false);\n    setShowWristSizeModal(false);\n    setShowHandGuide(true);\n  };\n\n  // Handle gender selection\n  const handleGenderChange = (gender) => {\n    setUserGender('men'); // Single gender role\n    setUserWristSize(50); // Set initial size for men\n    trackInteraction('size_adjustment', {\n      type: 'gender_change',\n      gender: 'men',\n      newWristSize: 50\n    });\n  };\n\n  // Handle wrist size change\n  const handleWristSizeChange = (size) => {\n    setUserWristSize(size);\n    trackInteraction('size_adjustment', {\n      type: 'wrist_size_change',\n      size\n    });\n  };\n\n  // Handle continue to product selection - No longer needed since both panels show together\n  // const handleContinueToProducts = () => {\n  //   setShowWristSizeInput(false);\n  //   setShowProductSelection(true);\n  // };\n\n  // Handle tab change\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  // Handle autocapture switch toggle\n  const handleAutoCaptureToggle = () => {\n    const newState = !isAutoCaptureEnabled;\n    setIsAutoCaptureEnabled(newState);\n\n    // Reset states when turning off\n    if (!newState) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      setIsHandInPosition(false);\n    }\n  };\n\n  // Reset autocapture when going back\n  const handleBackWithReset = () => {\n    setIsAutoCaptureEnabled(false);\n    setIsCountdownActive(false);\n    setCountdown(0);\n    setIsHandInPosition(false);\n    setShowWristSizeModal(false);\n    setUserWristSize(50); // Default to men's size\n    handleBack();\n  };\n\n  // Get current products based on active tab\n  const getCurrentProducts = () => {\n    return activeTab === 'Watches' ? watches : bracelets;\n  };\n\n  // Handle product selection\n  const handleProductSelect = (product) => {\n    applyProductToWatchPosition(product.path, activeTab);\n  };\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n  // Hand position monitoring effect (only when autocapture is enabled)\n  useEffect(() => {\n    if (!isAutoCaptureEnabled || isCaptured) return;\n\n    const interval = setInterval(() => {\n      const handInPosition = detectHandInPosition();\n      setIsHandInPosition(handInPosition);\n\n      // Only start countdown if hand is properly positioned\n      if (handInPosition && !isCountdownActive && countdown === 0) {\n        setIsCountdownActive(true);\n      }\n\n      // Stop countdown if hand moves out of position\n      if (!handInPosition && isCountdownActive) {\n        setIsCountdownActive(false);\n        setCountdown(0);\n      }\n    }, 150); // Check every 150ms for better performance\n\n    return () => clearInterval(interval);\n  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);\n\n  // Countdown effect (starts automatically when hand is in position)\n  useEffect(() => {\n    if (!isCountdownActive || isCaptured) {\n      setCountdown(0);\n      return;\n    }\n\n    // Only start countdown if hand is still in position\n    if (!isHandInPosition) {\n      setIsCountdownActive(false);\n      setCountdown(0);\n      return;\n    }\n\n    // Start countdown\n    setCountdown(3);\n\n    const countdownInterval = setInterval(() => {\n      setCountdown(prev => {\n        // Double-check hand position before continuing countdown\n        if (!isHandInPosition) {\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          return 0;\n        }\n\n        if (prev <= 1) {\n          // Countdown finished - trigger capture\n          clearInterval(countdownInterval);\n          setIsCountdownActive(false);\n          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture\n          handleCapture();\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(countdownInterval);\n  }, [isCountdownActive, isCaptured, isHandInPosition]);\n\n\n\n  // Add touch gesture handlers\n  const handleTouchStart = (e) => {\n    setIsDragging(true);\n    setStartY(e.touches[0].clientY);\n  };\n\n  const handleTouchMove = (e) => {\n    if (!isDragging) return;\n    \n    const currentY = e.touches[0].clientY;\n    const diff = currentY - startY;\n    \n    // Only allow dragging down\n    if (diff > 0) {\n      setPanelPosition(diff);\n    }\n  };\n\n  const handleTouchEnd = () => {\n    if (!isDragging) return;\n    \n    setIsDragging(false);\n    \n    // If dragged more than 100px down, close the panel\n    if (panelPosition > 100) {\n      setShowProductSelection(false);\n    }\n    \n    // Reset position\n    setPanelPosition(0);\n  };\n\n  // Add click outside handler for modals\n  useEffect(() => {\n    const handleClickOutside = (e) => {\n      if (showWristSizeModal && !e.target.closest('.modal-content')) {\n        setShowWristSizeModal(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('touchstart', handleClickOutside);\n    \n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('touchstart', handleClickOutside);\n    };\n  }, [showWristSizeModal]);\n\n  // Desktop QR Code Component\n  const DesktopQRCode = () => {\n    // Generate dynamic QR code URL based on URL parameters or default\n    const generateQRValue = () => {\n      const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';\n      const baseUrl = `${websiteUrl}/tryon`;\n\n      // If we have URL parameters, include them in the QR code\n      if (urlParams.image || urlParams.client || urlParams.size || urlParams.type) {\n        const params = new URLSearchParams();\n        if (urlParams.image) params.append('image', urlParams.image);\n        if (urlParams.client) params.append('client', urlParams.client);\n        if (urlParams.size) params.append('size', urlParams.size);\n        if (urlParams.type) params.append('type', urlParams.type);\n        return `${baseUrl}?${params.toString()}`;\n      }\n\n      // Default QR code for general try-on\n      return baseUrl;\n    };\n\n    const qrValue = generateQRValue();\n\n    return (\n      <div style={styles.desktopContainer}>\n        <div style={styles.qrContainer}>\n          <h2 style={styles.qrTitle}>Scan QR Code to Try On</h2>\n          <p style={styles.qrSubtitle}>\n            {urlParams.image\n              ? \"Scan to try on this specific product on your mobile device\"\n              : \"Open this page on your mobile device to experience the virtual try-on feature\"\n            }\n          </p>\n          <div style={styles.qrWrapper}>\n            <QRCodeSVG\n              value={qrValue}\n              size={256}\n              level=\"H\"\n              bgColor=\"#FFFFFF\"\n              fgColor=\"#2D8C88\"\n            />\n          </div>\n          <p style={styles.qrLink}>{qrValue}</p>\n          {urlParams.client && (\n            <p style={styles.clientInfo}>Client: {urlParams.client}</p>\n          )}\n          <button\n            style={styles.homeBtn}\n            onClick={onBackToHome}\n            aria-label=\"Home\"\n          >\n            ← Back to Home\n          </button>\n        </div>\n      </div>\n    );\n  };\n\n  // Return desktop view if not on mobile\n  if (isDesktop) {\n    return <DesktopQRCode />;\n  }\n\n  // Show intro popup before try-on UI\n  if (showIntroPopup) {\n    return (\n      <div style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100vw',\n        height: '100vh',\n        background: 'rgba(0,0,0,0.55)',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n      }}>\n        <div style={{\n          background: 'white',\n          borderRadius: 20,\n          padding: '32px 24px',\n          maxWidth: 340,\n          width: '90vw',\n          boxShadow: '0 8px 32px rgba(0,0,0,0.18)',\n          textAlign: 'center',\n        }}>\n          <h2 style={{ color: '#2D8C88', fontWeight: 700, fontSize: 22, marginBottom: 16 }}>Welcome to Virtual Try-On</h2>\n          <div style={{ color: '#333', fontSize: 16, marginBottom: 12 }}>\n            Please align your wrist and hand within the guide lines for the most accurate try-on experience.\n          </div>\n          <div style={{ color: '#666', fontSize: 14, marginBottom: 18 }}>\n            The product image is for illustration only. Actual size and fit may vary.\n          </div>\n          <button\n            style={{\n              background: '#2D8C88',\n              color: 'white',\n              border: 'none',\n              borderRadius: 12,\n              padding: '12px 32px',\n              fontWeight: 600,\n              fontSize: 16,\n              cursor: 'pointer',\n              boxShadow: '0 2px 8px rgba(45,140,136,0.12)'\n            }}\n            onClick={() => setShowIntroPopup(false)}\n            aria-label=\"OK, start try-on\"\n          >\n            OK, Start Try-On\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Update product selection panel JSX\n  return (\n    <div style={styles.container}>\n      <div style={styles.cameraContainer}>\n        <video\n          ref={videoRef}\n          style={styles.cameraFeed}\n          autoPlay\n          playsInline\n          muted\n          onPlaying={() => setIsCameraReady(true)}\n        />\n        <canvas ref={canvasRef} style={{ display: 'none' }} />\n        <img\n          ref={capturedImageRef}\n          style={styles.capturedImage}\n          alt=\"Captured hand\"\n        />\n\n        {/* Simple Home Button - Only visible when not captured */}\n        {!isCaptured && (\n          <button\n            style={styles.homeBtn}\n            onClick={() => window.history.back()}\n            aria-label=\"Back\"\n          >\n            ←\n          </button>\n        )}\n\n        {/* Autocapture Switch Button - Only visible when not captured */}\n        {!isCaptured && (\n          <div style={{\n            position: 'absolute',\n            top: isMobile ? 10 : 20,\n            right: isMobile ? 10 : 20,\n            zIndex: 20,\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            gap: '8px',\n            padding: '12px',\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: '20px',\n            backdropFilter: 'blur(10px)',\n            WebkitBackdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n            border: '1px solid rgba(255, 255, 255, 0.1)'\n          }}>\n            <label className=\"switch-label\">\n              Auto Capture\n            </label>\n            <label className=\"switch-container\">\n              <input\n                type=\"checkbox\"\n                checked={isAutoCaptureEnabled}\n                onChange={handleAutoCaptureToggle}\n                disabled={isCountdownActive}\n                aria-label=\"Toggle auto capture\"\n              />\n              <span className=\"switch-slider\"></span>\n            </label>\n          </div>\n        )}\n\n        {/* Simple Back Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.backBtn}\n            onClick={handleBackWithReset}\n            aria-label=\"Back\"\n          >\n            ←\n          </button>\n        )}\n\n        {/* Countdown Display - Only visible during active countdown */}\n        {isCountdownActive && (\n          <div style={styles.countdownDisplay}>\n            <div style={styles.countdownNumber}>{countdown}</div>\n            <div style={styles.countdownText}>Auto capturing...</div>\n          </div>\n        )}\n\n        {/* Status Messages */}\n        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={styles.statusText}>Position your arm and wrist in the guide area</div>\n            <div style={styles.statusSubtext}>Countdown will start automatically when detected</div>\n          </div>\n        )}\n\n        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (\n          <div style={styles.statusMessage}>\n            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>\n              Perfect! Starting countdown...\n            </div>\n          </div>\n        )}\n\n        {/* Always show this overlay above the camera feed, before capture */}\n        {!isCaptured && (\n          <div style={{\n            position: 'absolute',\n            top: '8%',\n            left: '50%',\n            transform: 'translateX(-50%)',\n            background: 'rgba(0,0,0,0.55)',\n            color: 'white',\n            padding: '10px 22px',\n            borderRadius: 16,\n            fontSize: 15,\n            fontWeight: 600,\n            zIndex: 12,\n            boxShadow: '0 2px 8px rgba(0,0,0,0.18)',\n            textAlign: 'center',\n            letterSpacing: 0.1,\n            maxWidth: 320\n          }}>\n            Align your wrist and hand within the white guide lines for best results.\n          </div>\n        )}\n\n        {/* Hand Guide SVG */}\n        {showHandGuide && (\n          <div\n            style={{\n              ...styles.handGuide,\n              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,\n              filter: isAutoCaptureEnabled && isHandInPosition\n                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'\n                : isAutoCaptureEnabled && !isHandInPosition\n                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'\n                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'\n            }}\n            className={isMobile ? 'mobile-hand-guide' : ''}\n            aria-hidden=\"true\"\n          >\n            <svg viewBox=\"0 0 800 600\" xmlns=\"http://www.w3.org/2000/svg\">\n              {/* Wrist guide lines */}\n              <path\n                d=\"M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              <path\n                d=\"M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"5\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n              />\n              {/* Wrist/Forearm area (rectangle) */}\n              <rect\n                x=\"320\"\n                y=\"150\"\n                width=\"160\"\n                height=\"300\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.25\" : \"0.15\"}\n                rx=\"15\"\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Hand area (circle) */}\n              <circle\n                cx=\"400\"\n                cy=\"300\"\n                r=\"60\"\n                fill={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                opacity={isAutoCaptureEnabled && isHandInPosition ? \"0.3\" : \"0.2\"}\n                stroke={\n                  isAutoCaptureEnabled && isHandInPosition\n                    ? \"#2D8C88\"\n                    : isAutoCaptureEnabled && !isHandInPosition\n                      ? \"#ff6b6b\"\n                      : \"white\"\n                }\n                strokeWidth=\"2\"\n              />\n              {/* Labels for clarity */}\n              {isAutoCaptureEnabled && (\n                <>\n                  <text x=\"400\" y=\"140\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    WRIST & FOREARM\n                  </text>\n                  <text x=\"400\" y=\"480\" textAnchor=\"middle\" fill=\"white\" fontSize=\"14\" fontWeight=\"bold\">\n                    HAND\n                  </text>\n                </>\n              )}\n            </svg>\n          </div>\n        )}\n\n        {/* Product Display - only show after capture */}\n        {isCaptured && selectedProduct && (\n          <>\n            {/* Text above product image */}\n            <div style={{\n              position: 'absolute',\n              top: '12%',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              background: 'rgba(255,255,255,0.92)',\n              color: '#2D8C88',\n              fontWeight: 600,\n              fontSize: 15,\n              borderRadius: 12,\n              padding: '8px 18px',\n              zIndex: 20,\n              boxShadow: '0 2px 8px rgba(45,140,136,0.10)',\n              textAlign: 'center',\n              maxWidth: 320\n            }}>\n              Product image is for illustration only. Actual size and fit may vary.\n            </div>\n            <div style={{\n              ...styles.productPosition,\n              width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,\n              height: activeTab === 'Watches'\n                ? (() => {\n                    const defaultWristSize = DEFAULT_WRIST_SIZE;\n                    const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);\n\n                    if (isLargeWrist) {\n                      // For large wrists, increase height by 40% to allow exceeding SVG height\n                      const sizeIncrease = (userWristSize - DEFAULT_WRIST_SIZE) / DEFAULT_WRIST_SIZE;\n                      return `${WATCH_HEIGHT * (1 + sizeIncrease * 0.9)}%`;\n                    }\n                    return `${WATCH_HEIGHT}%`;\n                  })()\n                : `${BRACELET_HEIGHT}%`,\n              // Apply clipping for wrist sizes >= 50mm (men) and >= 45mm (women)\n              clipPath: (() => {\n                const isLargeWrist = (userWristSize >= DEFAULT_WRIST_SIZE);\n                return activeTab === 'Watches' && isLargeWrist\n                  ? 'ellipse(220px 60px at 50% 50%)'\n                  : activeTab === 'Watches' && userWristSize < DEFAULT_WRIST_SIZE\n                    ? 'ellipse(220px 60px at 50% 50%)'\n                    : 'none';\n              })(),\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <img\n                  src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}\n                  alt=\"Selected product\"\n                  style={{\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'contain',\n                    transform: activeTab === 'Bracelets'\n                      ? (() => {\n                          // Use exact bracelet fitting logic from tryon.js with enhanced vertical flip\n                          const baseTransform = `rotate(90deg) scale(${BRACELET_HEIGHT / 30})`;\n\n                          // Apply realistic bracelet positioning based on hand detection\n                          // Bracelets need vertical flipping to appear correctly on the wrist\n                          if (isRightHand) {\n                            // For right hand: flip horizontally (like tryon.js) and add vertical flip for realism\n                            return `${baseTransform} scaleX(-1) scaleY(-1)`;\n                          } else {\n                            // For left hand: only vertical flip for proper bracelet orientation\n                            return `${baseTransform} scaleY(-1)`;\n                          }\n                        })()\n                      : (() => {\n                          const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];\n                          const adjustedWristSize = Math.max(userWristSize - WRIST_SIZE_OFFSET, MIN_ADJUSTED_WRIST_SIZE);\n                          const isLargeWrist = (userGender === 'men' && adjustedWristSize >= 50);\n\n                          if (isLargeWrist) {\n                            // For larger wrists, apply height scaling increase and allow exceeding SVG height\n                            const sizeIncrease = (adjustedWristSize - defaultWristSize) / defaultWristSize;\n                            const heightScale = 1 + (sizeIncrease * 0.4); // 40% height increase\n                            const widthScale = defaultWristSize / adjustedWristSize; // Decrease width as wrist increases\n\n                            return `scale(${(WATCH_HEIGHT / 25) * widthScale}) scaleX(${widthScale}) scaleY(${heightScale})`;\n                          }\n\n                          // For smaller wrists, use the original working logic with SVG height constraint\n                          return `scale(${Math.min(\n                            (WATCH_HEIGHT / 25) * (adjustedWristSize > defaultWristSize\n                              ? defaultWristSize / adjustedWristSize\n                              : defaultWristSize / adjustedWristSize),\n                            300 / (selectedProduct.dimensions?.totalHeight || 47) // Scale to match SVG height\n                          )}) scaleX(${adjustedWristSize > defaultWristSize\n                            ? defaultWristSize / adjustedWristSize\n                            : 1})`;\n                        })(),\n                    filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'\n                  }}\n                  onLoad={(e) => removeBackground(e.target, (urlParams.type === 'bracelets' || activeTab === 'Bracelets') ? 'bracelet' : 'watch')}\n                />\n                {((urlParams.type !== 'bracelets' && activeTab === 'Watches') || (!urlParams.type && activeTab === 'Watches')) && typeof selectedProduct === 'object' && (\n                  <div style={{\n                    position: 'absolute',\n                    bottom: '-30px',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    fontSize: '11px',\n                    fontWeight: '600',\n                    color: 'white',\n                    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                    padding: '3px 8px',\n                    borderRadius: '12px',\n                    whiteSpace: 'nowrap',\n                    pointerEvents: 'none',\n                    boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                    zIndex: 2\n                  }}>\n                    {selectedProduct.dialSize || selectedProduct.caseDiameter || urlParams.size || '42'}mm\n                    {userWristSize !== DEFAULT_WRIST_SIZE && (\n                      <span style={{\n                        fontSize: '10px',\n                        opacity: 0.8,\n                        marginLeft: '4px'\n                      }}>\n                        {(() => {\n                          const wristSizeRatio = DEFAULT_WRIST_SIZE / userWristSize;\n\n                          let scalingPercentage;\n                          if (userWristSize < DEFAULT_WRIST_SIZE) {\n                            // Realistic scaling for smaller wrists\n                            const sizeDifference = DEFAULT_WRIST_SIZE - userWristSize;\n                            const maxSizeDifference = DEFAULT_WRIST_SIZE * 0.25;\n                            const clampedDifference = Math.min(sizeDifference, maxSizeDifference);\n                            const moderateScaleFactor = 1 + (clampedDifference / DEFAULT_WRIST_SIZE) * 0.6;\n                            scalingPercentage = ((moderateScaleFactor - 1) * 100).toFixed(0);\n                          } else {\n                            // Standard scaling for larger wrists\n                            scalingPercentage = ((wristSizeRatio - 1) * 100).toFixed(0);\n                          }\n\n                          // Add debug indicator for large wrists\n                          const debugSuffix = userWristSize >= DEFAULT_WRIST_SIZE ? ' 🔥' : '';\n                          return `(${userWristSize < DEFAULT_WRIST_SIZE ? '+' : ''}${scalingPercentage}%)${debugSuffix}`;\n                        })()}\n                      </span>\n                    )}\n                  </div>\n                )}\n                {(urlParams.type === 'bracelets' || activeTab === 'Bracelets') && typeof selectedProduct === 'object' && (\n                  <div style={{\n                    position: 'absolute',\n                    bottom: '-30px',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    fontSize: '11px',\n                    fontWeight: '600',\n                    color: 'white',\n                    backgroundColor: 'rgba(45, 140, 136, 0.9)',\n                    padding: '3px 8px',\n                    borderRadius: '12px',\n                    whiteSpace: 'nowrap',\n                    pointerEvents: 'none',\n                    boxShadow: '0 2px 6px rgba(0,0,0,0.3)',\n                    zIndex: 2\n                  }}>\n                    {selectedProduct.braceletWidth || urlParams.size || '15'}mm width\n                  </div>\n                )}\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Camera-style Capture Button - only show before capture */}\n        {!isCaptured && (\n          <button\n            style={styles.captureBtn}\n            className={isMobile ? 'mobile-capture-btn' : ''}\n            onClick={handleCapture}\n            aria-label={isCaptured ? \"Select Products\" : \"Capture\"}\n            disabled={!isCameraReady}\n          >\n            <div style={styles.captureInner} className={isMobile ? 'mobile-inner-circle' : ''}></div>\n          </button>\n        )}\n        {/* Camera loading message */}\n        {!isCameraReady && (\n          <div style={{\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            background: 'rgba(0,0,0,0.7)',\n            color: 'white',\n            padding: '18px 32px',\n            borderRadius: 18,\n            fontSize: 18,\n            fontWeight: 600,\n            zIndex: 100\n          }}>\n            Loading camera...\n          </div>\n        )}\n\n        {/* Reset Button - Only visible when captured */}\n        {isCaptured && (\n          <button\n            style={styles.resetBtn}\n            onClick={() => window.location.reload()}\n            aria-label=\"Reset\"\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z\"/>\n            </svg>\n          </button>\n        )}\n      </div>\n\n      {/* Mobile Wrist Size Button - Top right corner */}\n      {isCaptured && (\n        <button\n          style={styles.wristSizeFloatingBtn}\n          className={isMobile ? 'mobile-btn' : ''}\n          onClick={() => setShowWristSizeModal(true)}\n          aria-label=\"Adjust wrist size\"\n        >\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"white\">\n            <path d=\"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"/>\n          </svg>\n          <span style={styles.wristSizeText}>{userWristSize}mm</span>\n        </button>\n      )}\n\n      {/* Wrist Size Modal - Mobile-friendly popup */}\n      {showWristSizeModal && (\n        <div \n          style={styles.modalOverlay} \n          onClick={() => setShowWristSizeModal(false)}\n          className=\"modal-overlay\"\n        >\n          <div \n            style={styles.wristSizeModal} \n            onClick={(e) => e.stopPropagation()}\n            className=\"modal-content\"\n          >\n            <div style={styles.modalHeader}>\n              <h3 style={styles.modalTitle}>Adjust Wrist Size</h3>\n              <button\n                style={styles.modalCloseBtn}\n                onClick={() => setShowWristSizeModal(false)}\n                aria-label=\"Close\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n                </svg>\n              </button>\n            </div>\n\n            <div style={styles.modalContent}>\n              {/* Gender Selection - Single Role */}\n              <div style={styles.genderSelection}>\n                <button\n                  style={{\n                    ...styles.genderButton,\n                    ...styles.genderButtonActive\n                  }}\n                  onClick={() => handleGenderChange('men')}\n                >\n                  Standard (50mm)\n                </button>\n              </div>\n\n              {/* Wrist Size Slider */}\n              <div style={styles.sliderContainer}>\n                <label style={styles.sliderLabel}>\n                  Wrist Size: {userWristSize}mm\n                </label>\n                <input\n                  type=\"range\"\n                  min={40}\n                  max={65}\n                  value={userWristSize}\n                  onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}\n                  style={styles.slider}\n                />\n                <div style={styles.sliderLabels}>\n                  <span>40mm</span>\n                  <span>65mm</span>\n                </div>\n\n                {/* Quick Size Presets */}\n                <div style={styles.presetButtons}>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZE)}\n                  >\n                    Average ({DEFAULT_WRIST_SIZE}mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(40)}\n                  >\n                    Small (40mm)\n                  </button>\n                  <button\n                    style={styles.presetButton}\n                    onClick={() => handleWristSizeChange(65)}\n                  >\n                    Large (65mm)\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Product Selection Panel */}\n      {showProductSelection && (\n        <div\n          ref={panelRef}\n          style={{\n            ...styles.productSelection,\n            transform: `translateY(${panelPosition}px)`,\n            touchAction: 'none'\n          }}\n          className={isMobile ? 'mobile-product-panel' : ''}\n          aria-modal=\"true\"\n          role=\"dialog\"\n          onTouchStart={handleTouchStart}\n          onTouchMove={handleTouchMove}\n          onTouchEnd={handleTouchEnd}\n        >\n          <div \n            style={styles.dragHandle} \n            aria-hidden=\"true\"\n            onTouchStart={handleTouchStart}\n            onTouchMove={handleTouchMove}\n            onTouchEnd={handleTouchEnd}\n          />\n          {/* Only show tabs if no specific product type is provided via URL */}\n          {!urlParams.type && (\n            <div style={styles.productTabs}>\n              <button\n                style={{\n                  ...styles.tab,\n                  ...(activeTab === 'Watches' ? styles.activeTab : {})\n                }}\n                onClick={() => handleTabChange('Watches')}\n              >\n                Watches\n              </button>\n              <button\n                style={{\n                  ...styles.tab,\n                  ...(activeTab === 'Bracelets' ? styles.activeTab : {})\n                }}\n                onClick={() => handleTabChange('Bracelets')}\n              >\n                Bracelets\n              </button>\n            </div>\n          )}\n          <div style={styles.productScroll} className=\"product-scroll\">\n            {getCurrentProducts().length > 0 ? (\n              getCurrentProducts().map((product, index) => {\n                // Simple null check only\n                if (!product) return null;\n\n                const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;\n\n                return (\n                  <button\n                    key={index}\n                    style={{\n                      ...styles.productItem,\n                      borderColor: isSelected ? '#2D8C88' : '#e9ecef',\n                      backgroundColor: isSelected ? '#f0fffe' : '#ffffff'\n                    }}\n                    title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}\n                    onClick={() => handleProductSelect(product)}\n                    aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}\n                  >\n                    <img\n                      src={product.path}\n                      alt={product.name}\n                      style={styles.productImage}\n                      onError={(e) => {\n                        e.target.parentElement.style.display = 'none';\n                      }}\n                    />\n                    <div style={styles.productLabel}>\n                      <div style={styles.productName}>{product.name}</div>\n                      {activeTab === 'Watches' && product.caseDiameter && (\n                        <div style={styles.productSize}>{product.caseDiameter}mm</div>\n                      )}\n                    </div>\n                  </button>\n                );\n              })\n            ) : (\n              <div style={styles.noProductsMessage}>\n                <div style={styles.noProductsIcon}>📱</div>\n                <div style={styles.noProductsTitle}>No Products Available</div>\n                <div style={styles.noProductsText}>\n                  This try-on experience is designed to be accessed through client websites with specific product parameters.\n                </div>\n                <div style={styles.noProductsSubtext}>\n                  Please visit a client's product page and click the \"Try On Virtually\" button.\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Styles object - Clean, modern design with improved mobile responsiveness\nconst styles = {\n  container: {\n    position: 'relative',\n    height: 'calc(var(--vh, 1vh) * 100)',\n    display: 'flex',\n    flexDirection: 'column',\n    backgroundColor: '#f8f9fa',\n    color: '#333',\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    overflow: 'hidden',\n    touchAction: 'manipulation',\n    WebkitTapHighlightColor: 'transparent',\n    WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n  },\n  cameraContainer: {\n    flex: 1,\n    position: 'relative',\n    overflow: 'hidden',\n    backgroundColor: '#000',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  cameraFeed: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    transform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n  capturedImage: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    display: 'none',\n    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring\n  },\n\n  homeBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  backBtn: {\n    position: 'absolute',\n    top: '20px',\n    left: '20px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    color: 'white',\n    padding: '12px',\n    borderRadius: '50%',\n    fontSize: '20px',\n    fontWeight: '700',\n    cursor: 'pointer',\n    zIndex: 10,\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '44px',\n    height: '44px',\n    outline: 'none'\n  },\n  switchContainer: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    zIndex: 10,\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '4px',\n    padding: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',\n    border: '1px solid rgba(255, 255, 255, 0.1)'\n  },\n  switchTrack: {\n    position: 'absolute',\n    top: '12px',\n    left: '12px',\n    width: '60px',\n    height: '32px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    borderRadius: '16px',\n    border: '2px solid rgba(255, 255, 255, 0.2)',\n    zIndex: 9,\n    transition: 'all 0.3s ease'\n  },\n  switchButton: {\n    position: 'relative',\n    width: '28px',\n    height: '28px',\n    borderRadius: '50%',\n    border: 'none',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 11,\n    margin: '2px',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    '&:hover': {\n      transform: 'scale(1.1)'\n    }\n  },\n  switchLabel: {\n    fontSize: '12px',\n    fontWeight: '700',\n    color: 'white',\n    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',\n    marginTop: '4px',\n    padding: '4px 12px',\n    borderRadius: '12px',\n    backgroundColor: 'rgba(0, 0, 0, 0.3)',\n    letterSpacing: '0.5px'\n  },\n  countdownDisplay: {\n    position: 'absolute',\n    top: '35%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '16px 24px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    borderRadius: '20px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  countdownNumber: {\n    fontSize: '72px',\n    fontWeight: '900',\n    color: '#2D8C88',\n    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',\n    marginBottom: '8px',\n    animation: 'pulse 1s ease-in-out'\n  },\n  countdownText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'\n  },\n  statusMessage: {\n    position: 'absolute',\n    top: '25%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    zIndex: 15,\n    textAlign: 'center',\n    pointerEvents: 'none',\n    padding: '12px 20px',\n    borderRadius: '16px',\n    backgroundColor: 'rgba(0, 0, 0, 0.4)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'\n  },\n  statusText: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(255, 107, 107, 0.9)',\n    padding: '12px 20px',\n    borderRadius: '25px',\n    marginBottom: '8px',\n    transition: 'all 0.3s ease'\n  },\n  statusSubtext: {\n    fontSize: '12px',\n    fontWeight: '500',\n    color: 'white',\n    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    padding: '6px 12px',\n    borderRadius: '15px'\n  },\n  handGuide: {\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    width: '80%',\n    maxWidth: '400px',\n    height: 'auto',\n    opacity: 0.8,\n    pointerEvents: 'none',\n    zIndex: 5,\n    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',\n    transition: 'all 0.3s ease'\n  },\nproductPosition: {\n  position: 'absolute',\n  top: '50%',\n  left: '50%',\n  transform: 'translate(-50%, -50%)',\n  zIndex: 8,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  width: '25vw', // width controlled\n  aspectRatio: '1 / 1.6', // maintain height-to-width ratio (adjust as needed)\n  minWidth: '100px',\n  minHeight: '160px', // fallback for unsupported aspect-ratio\n  pointerEvents: 'none'\n}\n,\n  captureBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '80px',\n    height: '80px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: '4px solid rgba(255, 255, 255, 0.3)',\n    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0,\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  captureInner: {\n    width: '60px',\n    height: '60px',\n    backgroundColor: '#2D8C88',\n    borderRadius: '50%',\n    transition: 'all 0.2s ease'\n  },\n  resetBtn: {\n    position: 'absolute',\n    bottom: '30px',\n    right: '30px',\n    width: '50px',\n    height: '50px',\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    zIndex: 10,\n    transition: 'all 0.2s ease',\n    border: 'none',\n    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',\n    outline: 'none',\n    padding: 0\n  },\n\n  // Wrist Size Floating Button - Follows CSS mobile patterns\n  wristSizeFloatingBtn: {\n    position: 'absolute',\n    top: '20px',\n    right: '20px',\n    backgroundColor: '#2D8C88',\n    color: 'white',\n    padding: '12px 16px',\n    borderRadius: '24px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    border: 'none',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    outline: 'none',\n    transition: 'all 0.2s ease',\n    zIndex: 15,\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)',\n    minHeight: '48px',\n    minWidth: '48px',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  wristSizeText: {\n    fontSize: '12px',\n    fontWeight: '600'\n  },\n\n  // Modal Styles\n  modalOverlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 50,\n    padding: '20px',\n    backdropFilter: 'blur(5px)',\n    WebkitBackdropFilter: 'blur(5px)',\n    touchAction: 'none'\n  },\n  wristSizeModal: {\n    backgroundColor: 'white',\n    borderRadius: '24px',\n    width: '100%',\n    maxWidth: '95vw',\n    maxHeight: '80vh',\n    overflow: 'hidden',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    margin: '10px',\n    position: 'relative'\n  },\n  modalHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    padding: '16px 20px 12px 20px',\n    borderBottom: '1px solid #f0f0f0'\n  },\n  modalTitle: {\n    fontSize: '18px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0\n  },\n  modalCloseBtn: {\n    width: '32px',\n    height: '32px',\n    borderRadius: '50%',\n    border: 'none',\n    backgroundColor: '#f5f5f5',\n    color: '#666',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'all 0.2s ease',\n    outline: 'none'\n  },\n  modalContent: {\n    padding: '16px 20px 20px 20px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '16px',\n    overflowY: 'auto',\n    WebkitOverflowScrolling: 'touch',\n    maxHeight: 'calc(80vh - 60px)'\n  },\n\n  // Mobile styles handled by CSS file\n  wristSizeContent: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '12px'\n  },\n  wristSizeTitle: {\n    fontSize: '20px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    margin: 0,\n    textAlign: 'center'\n  },\n  wristSizeSubtitle: {\n    fontSize: '14px',\n    color: '#666',\n    margin: 0,\n    textAlign: 'center',\n    lineHeight: '1.4'\n  },\n  genderSelection: {\n    display: 'flex',\n    gap: '12px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  genderButton: {\n    flex: 1,\n    padding: '12px 16px',\n    borderRadius: '10px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '2px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  genderButtonActive: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderColor: '#2D8C88',\n    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'\n  },\n  sliderContainer: {\n    width: '100%',\n    maxWidth: '300px',\n    display: 'flex',\n    flexDirection: 'column',\n    gap: '8px'\n  },\n  sliderLabel: {\n    fontSize: '16px',\n    fontWeight: '600',\n    color: '#333',\n    textAlign: 'center',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px'\n  },\n  sizeChange: {\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    backgroundColor: 'rgba(45, 140, 136, 0.1)',\n    padding: '2px 8px',\n    borderRadius: '12px'\n  },\n  slider: {\n    width: '100%',\n    height: '6px',\n    borderRadius: '3px',\n    background: '#e0e0e0',\n    outline: 'none',\n    cursor: 'pointer',\n    WebkitAppearance: 'none',\n    appearance: 'none'\n  },\n  sliderLabels: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    fontSize: '12px',\n    color: '#999',\n    marginTop: '4px'\n  },\n  presetButtons: {\n    display: 'flex',\n    gap: '8px',\n    width: '100%',\n    maxWidth: '300px'\n  },\n  presetButton: {\n    flex: 1,\n    padding: '8px 12px',\n    borderRadius: '8px',\n    fontSize: '12px',\n    fontWeight: '500',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    backgroundColor: '#ffffff',\n    color: '#666',\n    outline: 'none'\n  },\n  continueButton: {\n    width: '100%',\n    maxWidth: '300px',\n    padding: '14px 24px',\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    borderRadius: '12px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'\n  },\n\n  productSelection: {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    backgroundColor: 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    borderTopLeftRadius: '24px',\n    borderTopRightRadius: '24px',\n    padding: '16px',\n    maxHeight: '85vh',\n    display: 'flex',\n    flexDirection: 'column',\n    zIndex: 20,\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    transform: 'translateY(0)',\n    transition: 'transform 0.3s ease-out',\n    overflow: 'hidden',\n    touchAction: 'none',\n    willChange: 'transform',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n  closeBtn: {\n    position: 'absolute',\n    top: '12px',\n    right: '16px',\n    color: '#666',\n    cursor: 'pointer',\n    zIndex: 21,\n    width: '32px',\n    height: '32px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderRadius: '50%',\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    transition: 'all 0.2s ease',\n    border: 'none',\n    outline: 'none',\n    padding: 0\n  },\n  productTabs: {\n    display: 'flex',\n    marginBottom: '16px',\n    backgroundColor: '#f0f0f0',\n    borderRadius: '12px',\n    padding: '4px',\n    gap: '4px'\n  },\n  tab: {\n    flex: 1,\n    textAlign: 'center',\n    padding: '12px 16px',\n    borderRadius: '8px',\n    fontSize: '16px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    color: '#666',\n    outline: 'none',\n    border: 'none',\n    backgroundColor: 'transparent',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  activeTab: {\n    backgroundColor: '#2D8C88',\n    color: '#ffffff',\n    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'\n  },\n  productScroll: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(100px, 1fr))',\n    gap: '12px',\n    maxHeight: 'calc(85vh - 120px)',\n    overflowY: 'auto',\n    paddingBottom: '16px',\n    scrollbarWidth: 'thin',\n    scrollbarColor: '#ddd #f5f5f5',\n    WebkitOverflowScrolling: 'touch'\n  },\n  productItem: {\n    position: 'relative',\n    width: '100%',\n    aspectRatio: '1/1',\n    backgroundColor: '#ffffff',\n    borderRadius: '16px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    border: '1px solid #e0e0e0',\n    overflow: 'hidden',\n    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',\n    padding: '8px',\n    outline: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    touchAction: 'manipulation'\n  },\n  productImage: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'contain',\n    borderRadius: '8px',\n    backgroundColor: '#ffffff'\n  },\n  productLabel: {\n    position: 'absolute',\n    bottom: '3px',\n    left: '3px',\n    right: '3px',\n    fontSize: '9px',\n    color: '#666',\n    textAlign: 'center',\n    backgroundColor: 'rgba(255, 255, 255, 0.95)',\n    borderRadius: '3px',\n    padding: '3px 2px',\n    overflow: 'hidden'\n  },\n  productName: {\n    fontSize: '9px',\n    fontWeight: '600',\n    whiteSpace: 'nowrap',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    marginBottom: '1px'\n  },\n  productSize: {\n    fontSize: '8px',\n    fontWeight: '500',\n    color: '#2D8C88',\n    whiteSpace: 'nowrap'\n  },\n  dragHandle: {\n    width: '40px',\n    height: '4px',\n    backgroundColor: '#e0e0e0',\n    borderRadius: '2px',\n    margin: '0 auto 12px',\n    cursor: 'grab',\n    touchAction: 'none',\n    userSelect: 'none',\n    WebkitUserSelect: 'none'\n  },\n\n  desktopContainer: {\n    position: 'relative',\n    height: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#f8f9fa',\n    padding: '20px'\n  },\n  qrContainer: {\n    backgroundColor: 'white',\n    padding: '40px',\n    borderRadius: '24px',\n    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n    textAlign: 'center',\n    maxWidth: '500px',\n    width: '100%'\n  },\n  qrTitle: {\n    fontSize: '28px',\n    fontWeight: '700',\n    color: '#2D8C88',\n    marginBottom: '16px'\n  },\n  qrSubtitle: {\n    fontSize: '16px',\n    color: '#666',\n    marginBottom: '32px',\n    lineHeight: '1.5'\n  },\n  qrWrapper: {\n    backgroundColor: 'white',\n    padding: '20px',\n    borderRadius: '16px',\n    display: 'inline-block',\n    marginBottom: '24px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'\n  },\n  qrLink: {\n    fontSize: '14px',\n    color: '#2D8C88',\n    marginBottom: '32px',\n    wordBreak: 'break-all'\n  },\n  clientInfo: {\n    fontSize: '12px',\n    color: '#666',\n    marginBottom: '16px',\n    fontStyle: 'italic'\n  },\n  noProductsMessage: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: '40px 20px',\n    textAlign: 'center',\n    height: '200px'\n  },\n  noProductsIcon: {\n    fontSize: '48px',\n    marginBottom: '16px'\n  },\n  noProductsTitle: {\n    fontSize: '18px',\n    fontWeight: '600',\n    color: '#333',\n    marginBottom: '12px'\n  },\n  noProductsText: {\n    fontSize: '14px',\n    color: '#666',\n    lineHeight: '1.5',\n    marginBottom: '8px',\n    maxWidth: '280px'\n  },\n  noProductsSubtext: {\n    fontSize: '12px',\n    color: '#999',\n    lineHeight: '1.4',\n    maxWidth: '260px'\n  }\n};\n\nexport default Tryon;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,cAAc,CAAC,CAAC;AAC1C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,uBAAuB,QAAQ,4BAA4B;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,cAAc,CAAC,0BAA0B,CAAC,EAAE;EAC3F,MAAMC,YAAY,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,EAAE,GAAG,0BAA0B;EAC5CF,YAAY,CAACG,WAAW,GAAGN,SAAS;EACpCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACL,YAAY,CAAC;AACzC;AAEA,MAAMM,KAAK,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClC;EACA,MAAMC,QAAQ,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMqB,gBAAgB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsB,SAAS,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC;IACzC2C,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC;IACrDuD,sBAAsB,EAAE,IAAI;IAC5BC,eAAe,EAAE,CAAC;IAClBC,wBAAwB,EAAE,KAAK;IAC/BC,oBAAoB,EAAE,KAAK;IAC3BC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE,KAAK;IACxBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE;MACZC,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,gBAAgB,EAAE;MAChBC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpF,QAAQ,CAAC;IAC3DqF,SAAS,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IACnDC,WAAW,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IAClCC,cAAc,EAAE,CAAC,CAAC;IAClBC,aAAa,EAAE,CAAC,CAAC;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjG,QAAQ,CAAC;IACnDkG,qBAAqB,EAAE,CAAC;IACxBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,qBAAqB,EAAE,CAAC;IACxBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxG,QAAQ,CAAC;IACvDyG,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,KAAK;IACrBC,cAAc,EAAE,KAAK;IACrBC,YAAY,EAAE,KAAK;IACnBC,WAAW,EAAE,KAAK;IAClBC,mBAAmB,EAAE,KAAK;IAC1BC,iBAAiB,EAAE;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACkH,aAAa,EAAEC,gBAAgB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACoH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErE;EACA,MAAM,CAACsH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvH,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACwH,SAAS,EAAEC,YAAY,CAAC,GAAGzH,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC0H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC8H,aAAa,EAAEC,gBAAgB,CAAC,GAAG/H,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACgI,UAAU,EAAEC,aAAa,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkI,MAAM,EAAEC,SAAS,CAAC,GAAGnI,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAMoI,QAAQ,GAAGlI,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACA,MAAM,CAACmI,cAAc,EAAEC,iBAAiB,CAAC,GAAGtI,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAMuI,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,IAAI;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClBhG,IAAI,EAAE,OAAO;IACbiG,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,IAAI;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,IAAI;IAAE;IACnBC,YAAY,EAAE,EAAE;IAAE;IAClBhG,IAAI,EAAE,OAAO;IACbiG,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,GAAG;IAAE;IACpBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClBhG,IAAI,EAAE,QAAQ;IACdiG,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,IAAI;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClBhG,IAAI,EAAE,YAAY;IAClBiG,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,CAAC;IAAE;IAClBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClBhG,IAAI,EAAE,YAAY;IAClBiG,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,qBAAqB;IAC3B;IACAC,YAAY,EAAE,EAAE;IAAE;IAClBC,aAAa,EAAE,EAAE;IAAE;IACnBC,UAAU,EAAE,EAAE;IAAE;IAChBC,WAAW,EAAE,EAAE;IAAE;IACjBC,YAAY,EAAE,EAAE;IAAE;IAClBhG,IAAI,EAAE,SAAS;IACfiG,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAER,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAA2B,CAAC,EACzD;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC5D;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAA2B,CAAC,EAC1D;IAAED,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAA2B,CAAC,CAC7D;;EAED;EACA,MAAMQ,gBAAgB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,WAAW,GAAG,OAAO,KAAK;IACpE,IAAI;MACF,IAAIC,iBAAiB,GAAGF,UAAU,CAACG,GAAG;;MAEtC;MACA,IAAIF,WAAW,KAAK,WAAW,EAAE;QAC/B,IAAI;UACF;UACAC,iBAAiB,GAAG,MAAMhJ,YAAY,CAAC8I,UAAU,CAACG,GAAG,CAAC;UACtDC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;UAEhE;UACAH,iBAAiB,GAAG,MAAM/I,uBAAuB,CAAC+I,iBAAiB,EAAED,WAAW,CAAC;UACjFG,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;UAE/D;UACAL,UAAU,CAACG,GAAG,GAAGD,iBAAiB;QAEpC,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdF,OAAO,CAACG,IAAI,CAAC,sCAAsC,EAAED,KAAK,CAAC;UAC3D;UACA,MAAME,sBAAsB,CAACR,UAAU,EAAEC,WAAW,CAAC;QACvD;MACF,CAAC,MAAM;QACL;QACA,MAAMQ,iBAAiB,GAAG,MAAMtJ,uBAAuB,CAAC+I,iBAAiB,EAAED,WAAW,CAAC;QACvFD,UAAU,CAACG,GAAG,GAAGM,iBAAiB;MACpC;;MAEA;MACAT,UAAU,CAACU,KAAK,CAACC,MAAM,GAAG,MAAM;MAChCX,UAAU,CAACU,KAAK,CAACE,YAAY,GAAG,QAAQ;MACxCZ,UAAU,CAACU,KAAK,CAACG,OAAO,GAAG,GAAG;MAE9BT,OAAO,CAACC,GAAG,CAAC,oCAAoCJ,WAAW,EAAE,CAAC;IAEhE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,qCAAqC,EAAED,KAAK,CAAC;MAC1D;MACA,MAAME,sBAAsB,CAACR,UAAU,EAAEC,WAAW,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMO,sBAAsB,GAAG,MAAAA,CAAOR,UAAU,EAAEC,WAAW,KAAK;IAChE,IAAI;MACFG,OAAO,CAACC,GAAG,CAAC,4CAA4CJ,WAAW,EAAE,CAAC;MAEtE,IAAIA,WAAW,KAAK,UAAU,EAAE;QAC9B;QACA,MAAMa,YAAY,GAAG,MAAM5J,YAAY,CAAC8I,UAAU,CAACG,GAAG,CAAC;QACvDH,UAAU,CAACG,GAAG,GAAGW,YAAY;MAC/B;;MAEA;MACA,MAAMC,MAAM,GAAGtJ,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMoJ,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnCF,MAAM,CAACG,KAAK,GAAGlB,UAAU,CAACmB,YAAY;MACtCJ,MAAM,CAACK,MAAM,GAAGpB,UAAU,CAACqB,aAAa;MACxCL,GAAG,CAACM,SAAS,CAACtB,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;MAE/B,MAAMuB,SAAS,GAAGP,GAAG,CAACQ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;MACrE,MAAMK,IAAI,GAAGF,SAAS,CAACE,IAAI;;MAE3B;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACvC,MAAME,CAAC,GAAGH,IAAI,CAACC,CAAC,CAAC;QACjB,MAAMG,CAAC,GAAGJ,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC;QACrB,MAAMI,CAAC,GAAGL,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC;;QAErB;QACA,MAAMK,SAAS,GAAG9B,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;QACrD,IAAI2B,CAAC,GAAGG,SAAS,IAAIF,CAAC,GAAGE,SAAS,IAAID,CAAC,GAAGC,SAAS,IAC/CC,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,IAAIG,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,EAAE;UAC9CL,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnB;MACF;MAEAV,GAAG,CAACkB,YAAY,CAACX,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;MACjCvB,UAAU,CAACG,GAAG,GAAGY,MAAM,CAACoB,SAAS,CAAC,WAAW,CAAC;MAE9C/B,OAAO,CAACC,GAAG,CAAC,6CAA6CJ,WAAW,EAAE,CAAC;IACzE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;;EAED;EACA,MAAM8B,qBAAqB,GAAIb,SAAS,IAAK;IAC3C;IACA,OAAOS,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,GAAG;EAC5B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BC,gBAAgB,CAAC,KAAK,CAAC;IACvB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzB3B,KAAK,EAAE;YAAE4B,KAAK,EAAE;UAAK,CAAC;UACtB1B,MAAM,EAAE;YAAE0B,KAAK,EAAE;UAAK;QACxB;MACF,CAAC,CAAC;MACF,IAAI1K,QAAQ,CAAC2K,OAAO,EAAE;QACpB3K,QAAQ,CAAC2K,OAAO,CAACC,SAAS,GAAGR,MAAM;MACrC;IACF,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZ7C,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAE2C,GAAG,CAAC;MAC7C;MACA,IAAI5K,gBAAgB,CAAC0K,OAAO,EAAE;QAC5B1K,gBAAgB,CAAC0K,OAAO,CAAC5C,GAAG,GAAG,iBAAiB;QAChD9H,gBAAgB,CAAC0K,OAAO,CAACrC,KAAK,CAACwC,OAAO,GAAG,OAAO;MAClD;MACA9C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C7H,aAAa,CAAC,IAAI,CAAC;MACnBM,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM,CAACqK,aAAa,EAAEZ,gBAAgB,CAAC,GAAGzL,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMsM,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAChL,QAAQ,CAAC2K,OAAO,IAAI,CAACzK,SAAS,CAACyK,OAAO,IAAI,CAACI,aAAa,EAAE,OAAO,IAAI;IAC1E,MAAMP,KAAK,GAAGxK,QAAQ,CAAC2K,OAAO;IAC9B,IAAI,CAACH,KAAK,CAACS,UAAU,IAAI,CAACT,KAAK,CAACU,WAAW,EAAE,OAAO,IAAI;IACxD,MAAMvC,MAAM,GAAGzI,SAAS,CAACyK,OAAO;IAChChC,MAAM,CAACG,KAAK,GAAG0B,KAAK,CAACS,UAAU;IAC/BtC,MAAM,CAACK,MAAM,GAAGwB,KAAK,CAACU,WAAW;IACjC,MAAMtC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACM,SAAS,CAACsB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE7B,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;IACvD,OAAOL,MAAM,CAACoB,SAAS,CAAC,WAAW,CAAC;EACtC,CAAC;;EAID;EACApL,SAAS,CAAC,MAAM;IACd,MAAMwM,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;MACnE,MAAMC,MAAM,GAAG;QACbpK,KAAK,EAAE+J,eAAe,CAACM,GAAG,CAAC,OAAO,CAAC;QACnCpK,MAAM,EAAE8J,eAAe,CAACM,GAAG,CAAC,QAAQ,CAAC;QACrCnK,IAAI,EAAE6J,eAAe,CAACM,GAAG,CAAC,MAAM,CAAC;QACjClK,IAAI,EAAE4J,eAAe,CAACM,GAAG,CAAC,MAAM;MAClC,CAAC;MAED1D,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEwD,MAAM,CAAC;MAC7CrK,YAAY,CAACqK,MAAM,CAAC;;MAEpB;MACA,IAAIA,MAAM,CAACpK,KAAK,EAAE;QAChB,IAAI;UACF;UACA,MAAMsK,QAAQ,GAAG,IAAIC,GAAG,CAACH,MAAM,CAACpK,KAAK,CAAC;;UAEtC;UACA,MAAMwG,WAAW,GAAG4D,MAAM,CAACjK,IAAI,IAAI,SAAS;UAC5C,MAAMqK,WAAW,GAAGhE,WAAW,KAAK,WAAW,GAAG,EAAE,GAAG,EAAE;UACzD,MAAMiE,UAAU,GAAGC,QAAQ,CAACN,MAAM,CAAClK,IAAI,CAAC,IAAIsK,WAAW;;UAEvD;UACAP,MAAM,CAACU,cAAc,GAAG;YACtB9E,IAAI,EAAE,gBAAgB;YACtBC,IAAI,EAAEsE,MAAM,CAACpK,KAAK;YAClB+F,YAAY,EAAES,WAAW,KAAK,SAAS,GAAGiE,UAAU,GAAG,IAAI;YAC3DG,aAAa,EAAEpE,WAAW,KAAK,WAAW,GAAGiE,UAAU,GAAG,IAAI;YAC9DzE,aAAa,EAAEQ,WAAW,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI;YACpDP,UAAU,EAAEwE,UAAU;YACtBvE,WAAW,EAAEM,WAAW,KAAK,SAAS,GAAGiE,UAAU,GAAG,IAAI,GAAGA,UAAU,GAAG,CAAC;YAC3EtE,YAAY,EAAEK,WAAW,KAAK,SAAS,GAAGiE,UAAU,GAAG,IAAI,GAAG,IAAI;YAClEtK,IAAI,EAAEqG,WAAW;YACjBqE,QAAQ,EAAET,MAAM,CAACnK;UACnB,CAAC;UAED0G,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;YACpD5G,KAAK,EAAEoK,MAAM,CAACpK,KAAK;YACnBC,MAAM,EAAEmK,MAAM,CAACnK,MAAM;YACrBC,IAAI,EAAEkK,MAAM,CAAClK;UACf,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO2G,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEuD,MAAM,CAACpK,KAAK,CAAC;UAC1D;UACAX,uBAAuB,CAAC,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;IAEDyK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxM,SAAS,CAAC,MAAM;IACd,IAAIwC,SAAS,CAACG,MAAM,IAAIH,SAAS,CAACE,KAAK,EAAE;MACvC2G,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE9G,SAAS,CAAC;MACjEgL,qBAAqB,CAAC,CAAC;;MAEvB;MACA,MAAMC,OAAO,GAAGC,0BAA0B,CAAC,CAAC;;MAE5C;MACAnH,mBAAmB,CAACoH,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACPlH,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMmH,kBAAkB,GAAGA,CAAA,KAAM;QAC/BC,mBAAmB,CAAC,WAAW,CAAC;MAClC,CAAC;MAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;QACnC,IAAIpN,QAAQ,CAACqN,eAAe,KAAK,QAAQ,EAAE;UACzCF,mBAAmB,CAAC,WAAW,CAAC;QAClC;MACF,CAAC;MAEDlB,MAAM,CAACqB,gBAAgB,CAAC,cAAc,EAAEJ,kBAAkB,CAAC;MAC3DlN,QAAQ,CAACsN,gBAAgB,CAAC,kBAAkB,EAAEF,sBAAsB,CAAC;MAErE,OAAO,MAAM;QACXL,OAAO,CAAC,CAAC;QACTd,MAAM,CAACsB,mBAAmB,CAAC,cAAc,EAAEL,kBAAkB,CAAC;QAC9DlN,QAAQ,CAACuN,mBAAmB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;QACxED,mBAAmB,CAAC,WAAW,CAAC;MAClC,CAAC;IACH,CAAC,MAAM;MACLxE,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;QACxD4E,SAAS,EAAE,CAAC,CAAC1L,SAAS,CAACG,MAAM;QAC7BwL,QAAQ,EAAE,CAAC,CAAC3L,SAAS,CAACE;MACxB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;;EAEf;EACAxC,SAAS,CAAC,MAAM;IACd,MAAMoO,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,cAAc,GAAG1B,MAAM,CAAC2B,UAAU,IAAI,GAAG;MAC/CjM,WAAW,CAACgM,cAAc,CAAC;MAC3B9L,YAAY,CAAC,CAAC8L,cAAc,CAAC;IAC/B,CAAC;;IAED;IACA,MAAME,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAMC,EAAE,GAAG7B,MAAM,CAAC8B,WAAW,GAAG,IAAI;MACpC/N,QAAQ,CAACgO,eAAe,CAAC/E,KAAK,CAACgF,WAAW,CAAC,MAAM,EAAE,GAAGH,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDJ,WAAW,CAAC,CAAC;IACbG,KAAK,CAAC,CAAC;IAEP5B,MAAM,CAACqB,gBAAgB,CAAC,QAAQ,EAAE,MAAM;MACtCI,WAAW,CAAC,CAAC;MACbG,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IAEF5B,MAAM,CAACqB,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;MACjDY,UAAU,CAAC,MAAM;QACfL,KAAK,CAAC,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEF,OAAO,MAAM;MACX5B,MAAM,CAACsB,mBAAmB,CAAC,QAAQ,EAAEG,WAAW,CAAC;MACjDzB,MAAM,CAACsB,mBAAmB,CAAC,mBAAmB,EAAEM,KAAK,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvO,SAAS,CAAC,MAAM;IACd,IAAI,OAAO2M,MAAM,KAAK,WAAW,IAAIjB,SAAS,CAACC,YAAY,EAAE;MAC3DJ,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMsD,kBAAkB,GAAG,EAAE,CAAC,CAAC;EAC/B,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;;EAE9B;EACA,MAAMC,mBAAmB,GAAG;IAC1BC,GAAG,EAAE,EAAE,CAAI;EACb,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;EAC9B,MAAMC,uBAAuB,GAAG,EAAE,CAAC,CAAC;;EAEpC,MAAMC,yBAAyB,GAAG,GAAG,CAAC,CAAC;EACvC,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;EAC/B,MAAMC,kBAAkB,GAAG,GAAG,CAAC,CAAC;;EAEhC;EACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,cAAc,EAAEC,eAAe,KAAK;IAC3E;IACA,MAAMC,gBAAgB,GAAGf,mBAAmB,CAAClI,UAAU,CAAC;;IAExD;IACA,MAAMkJ,iBAAiB,GAAGhF,IAAI,CAAC1F,GAAG,CAAC0B,aAAa,GAAGkI,iBAAiB,EAAEC,uBAAuB,CAAC;;IAE9F;IACA,MAAMc,qBAAqB,GAAGF,gBAAgB,GAAGC,iBAAiB;;IAElE;IACA,MAAME,YAAY,GAAGd,yBAAyB,GAAGY,iBAAiB;;IAElE;IACA,MAAMG,aAAa,GAAGP,KAAK,CAAClH,UAAU,GAAGwH,YAAY;IACrD,MAAME,cAAc,GAAGR,KAAK,CAACjH,WAAW,GAAGuH,YAAY;IACvD,MAAMG,eAAe,GAAGT,KAAK,CAAChH,YAAY,GAAGsH,YAAY;;IAEzD;IACA,MAAMI,iBAAiB,GAAIH,aAAa,GAAGd,iBAAiB,GAAI,GAAG;IACnE,MAAMkB,kBAAkB,GAAIH,cAAc,GAAGd,kBAAkB,GAAI,GAAG;IACtE,MAAMkB,mBAAmB,GAAIH,eAAe,GAAGhB,iBAAiB,GAAI,GAAG;;IAEvE;IACA,MAAMoB,SAAS,GAAG,EAAE,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAC;;IAEtB,OAAO;MACLxG,KAAK,EAAEc,IAAI,CAAC1F,GAAG,CAACgL,iBAAiB,EAAE,CAAC,CAAC;MAAE;MACvClG,MAAM,EAAEY,IAAI,CAAC1F,GAAG,CAACiL,kBAAkB,EAAE,EAAE,CAAC;MAAE;MAC1C3H,YAAY,EAAE4H,mBAAmB;MACjCC,SAAS;MACTC,SAAS;MACTC,KAAK,EAAE3F,IAAI,CAAC3F,GAAG,CAACiL,iBAAiB,GAAG,EAAE,EAAEC,kBAAkB,GAAG,EAAE,CAAC,GAAGN,qBAAqB;MACxFW,SAAS,EAAEhB,KAAK,CAAClH,UAAU;MAC3BmI,UAAU,EAAEjB,KAAK,CAACjH,WAAW;MAC7BH,YAAY,EAAEoH,KAAK,CAACpH,YAAY;MAChCsI,cAAc,EAAEb,qBAAqB;MACrCD;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMe,gBAAgB,GAAGA,CAACC,SAAS,EAAErP,WAAW,KAAK;IACnD,MAAMsP,cAAc,GAAGtB,wBAAwB,CAACqB,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEpE;IACA;IACA,IAAIE,SAAS,GAAGD,cAAc,CAACR,SAAS;IACxC,IAAIU,SAAS,GAAGF,cAAc,CAACP,SAAS,GAAG,CAAC,CAAC,CAAC;;IAE9C;IACA,IAAI/O,WAAW,EAAE;MACfuP,SAAS,GAAGD,cAAc,CAACR,SAAS,GAAG,CAAC,CAAC,CAAC;IAC5C;;IAEA;IACA,MAAMW,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAMC,WAAW,GAAGL,SAAS,CAACrI,WAAW;IACzC,MAAM2I,gBAAgB,GAAGF,SAAS,GAAGC,WAAW;;IAEhD;IACA,QAAQL,SAAS,CAACpO,IAAI;MACpB,KAAK,YAAY;QACf;QACAuO,SAAS,IAAI,CAAC;QACd;MACF,KAAK,QAAQ;QACX;QACAA,SAAS,IAAI,GAAG;QAChB;MACF,KAAK,OAAO;QACV;QACAA,SAAS,IAAI,GAAG;QAChB;MACF;QACE;IACJ;IAEA,OAAO;MACL,GAAGF,cAAc;MACjBR,SAAS,EAAES,SAAS;MACpBR,SAAS,EAAES,SAAS;MACpBR,KAAK,EAAE3F,IAAI,CAAC3F,GAAG,CAAC4L,cAAc,CAACN,KAAK,EAAEW,gBAAgB,CAAC,CAAC;IAC1D,CAAC;EACH,CAAC;;EAED;;EAEA;EACA,MAAM7D,0BAA0B,GAAGA,CAAA,KAAM;IACvC;IACA,IAAI8D,aAAa,GAAG,CAAC;IACrB,IAAIC,YAAY,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAEjC,MAAMC,eAAe,GAAIC,CAAC,IAAK;MAC7B,MAAMC,QAAQ,GAAG7G,IAAI,CAAC8G,IAAI,CACxB9G,IAAI,CAAC+G,GAAG,CAACH,CAAC,CAACI,OAAO,GAAGR,YAAY,CAACC,CAAC,EAAE,CAAC,CAAC,GACvCzG,IAAI,CAAC+G,GAAG,CAACH,CAAC,CAACK,OAAO,GAAGT,YAAY,CAACE,CAAC,EAAE,CAAC,CACxC,CAAC;MACDH,aAAa,IAAIM,QAAQ;MACzBL,YAAY,GAAG;QAAEC,CAAC,EAAEG,CAAC,CAACI,OAAO;QAAEN,CAAC,EAAEE,CAAC,CAACK;MAAQ,CAAC;MAE7C7O,kBAAkB,CAACsK,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACP7J,cAAc,EAAE0N;MAClB,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGzF,MAAM,CAAC0F,WAAW,IAAI3R,QAAQ,CAACgO,eAAe,CAAC0D,SAAS;MAC1E,MAAME,SAAS,GAAG5R,QAAQ,CAACgO,eAAe,CAAC6D,YAAY,GAAG5F,MAAM,CAAC8B,WAAW;MAC5E,MAAM+D,aAAa,GAAGvH,IAAI,CAACwH,KAAK,CAAEL,SAAS,GAAGE,SAAS,GAAI,GAAG,CAAC;MAE/DjP,kBAAkB,CAACsK,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACP9J,WAAW,EAAEoH,IAAI,CAAC1F,GAAG,CAACoI,IAAI,CAAC9J,WAAW,EAAE2O,aAAa;MACvD,CAAC,CAAC,CAAC;MAEHE,gBAAgB,CAAC,QAAQ,EAAE;QAAEC,KAAK,EAAEH,aAAa;QAAEI,QAAQ,EAAER;MAAU,CAAC,CAAC;IAC3E,CAAC;;IAED;IACA,IAAIS,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC/B,IAAIC,aAAa,GAAG,IAAI;IAExB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAI,CAACD,aAAa,EAAE;QAClB3P,kBAAkB,CAACsK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP9I,gBAAgB,EAAE;YAChB,GAAG8I,IAAI,CAAC9I,gBAAgB;YACxBG,YAAY,EAAE2I,IAAI,CAAC9I,gBAAgB,CAACG,YAAY,GAAG;UACrD;QACF,CAAC,CAAC,CAAC;QACH6N,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC3BC,aAAa,GAAG,IAAI;MACtB;IACF,CAAC;IAED,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIF,aAAa,EAAE;QACjB,MAAMlO,SAAS,GAAGgO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,cAAc;QAC7CxP,kBAAkB,CAACsK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACP9I,gBAAgB,EAAE;YAChB,GAAG8I,IAAI,CAAC9I,gBAAgB;YACxBC,SAAS,EAAE6I,IAAI,CAAC9I,gBAAgB,CAACC,SAAS,GAAGA,SAAS;YACtDC,UAAU,EAAE4I,IAAI,CAAC9I,gBAAgB,CAACE,UAAU,GAAG;UACjD;QACF,CAAC,CAAC,CAAC;QACHiO,aAAa,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMG,gBAAgB,GAAItB,CAAC,IAAK;MAC9B,IAAIA,CAAC,CAACK,OAAO,IAAI,CAAC,EAAE;QAClB7O,kBAAkB,CAACsK,IAAI,KAAK;UAC1B,GAAGA,IAAI;UACPlJ,UAAU,EAAE;YACVC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,IAAImO,IAAI,CAAC,CAAC;YACrBlO,gBAAgB,EAAE,CAAC0B,gBAAgB,CAACQ;UACtC;QACF,CAAC,CAAC,CAAC;QACH4L,gBAAgB,CAAC,aAAa,EAAE;UAAE/N,SAAS,EAAE,IAAImO,IAAI,CAAC;QAAE,CAAC,CAAC;MAC5D;IACF,CAAC;;IAED;IACA,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAI,QAAQ,IAAIC,WAAW,EAAE;QAC3B,MAAMC,MAAM,GAAGD,WAAW,CAACC,MAAM;QACjCnO,qBAAqB,CAACwI,IAAI,KAAK;UAC7B,GAAGA,IAAI;UACPlI,WAAW,EAAE;YACXC,IAAI,EAAE4N,MAAM,CAACC,cAAc;YAC3B5N,KAAK,EAAE2N,MAAM,CAACE;UAChB;QACF,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAI,YAAY,IAAI9H,SAAS,EAAE;QAC7B,MAAM+H,UAAU,GAAG/H,SAAS,CAAC+H,UAAU;QACvCtO,qBAAqB,CAACwI,IAAI,KAAK;UAC7B,GAAGA,IAAI;UACP/H,cAAc,EAAE;YACd8N,cAAc,EAAED,UAAU,CAAC5Q,IAAI;YAC/B8Q,QAAQ,EAAEF,UAAU,CAACE,QAAQ;YAC7BC,GAAG,EAAEH,UAAU,CAACG,GAAG;YACnBC,aAAa,EAAEJ,UAAU,CAACI;UAC5B;QACF,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACAnT,QAAQ,CAACsN,gBAAgB,CAAC,WAAW,EAAE4D,eAAe,CAAC;IACvDjF,MAAM,CAACqB,gBAAgB,CAAC,QAAQ,EAAEmE,YAAY,CAAC;IAC/CxF,MAAM,CAACqB,gBAAgB,CAAC,OAAO,EAAEiF,WAAW,CAAC;IAC7CtG,MAAM,CAACqB,gBAAgB,CAAC,MAAM,EAAEkF,UAAU,CAAC;IAC3CxS,QAAQ,CAACsN,gBAAgB,CAAC,YAAY,EAAEmF,gBAAgB,CAAC;;IAEzD;IACAC,kBAAkB,CAAC,CAAC;IACpB,MAAMU,mBAAmB,GAAGC,WAAW,CAACX,kBAAkB,EAAE,IAAI,CAAC;;IAEjE;IACA,OAAO,MAAM;MACX1S,QAAQ,CAACuN,mBAAmB,CAAC,WAAW,EAAE2D,eAAe,CAAC;MAC1DjF,MAAM,CAACsB,mBAAmB,CAAC,QAAQ,EAAEkE,YAAY,CAAC;MAClDxF,MAAM,CAACsB,mBAAmB,CAAC,OAAO,EAAEgF,WAAW,CAAC;MAChDtG,MAAM,CAACsB,mBAAmB,CAAC,MAAM,EAAEiF,UAAU,CAAC;MAC9CxS,QAAQ,CAACuN,mBAAmB,CAAC,YAAY,EAAEkF,gBAAgB,CAAC;MAC5Da,aAAa,CAACF,mBAAmB,CAAC;IACpC,CAAC;EACH,CAAC;;EAED;EACA,MAAMG,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,CAAC;MACjE,MAAMzJ,IAAI,GAAG,MAAMwJ,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,OAAO1J,IAAI,CAAC2J,EAAE;IAChB,CAAC,CAAC,OAAO9K,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,wBAAwB,EAAED,KAAK,CAAC;MAC7C,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMiE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAChL,SAAS,CAACG,MAAM,IAAI,CAACH,SAAS,CAACE,KAAK,EAAE;MACzC2G,OAAO,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC5D;IACF;IAEA,IAAI;MACF,MAAM8K,iBAAiB,GAAGjB,WAAW,CAACN,GAAG,CAAC,CAAC;MAC3C,MAAMwB,MAAM,GAAG,MAAMN,SAAS,CAAC,CAAC;MAEhC,MAAMO,WAAW,GAAG;QAClBjH,QAAQ,EAAE/K,SAAS,CAACG,MAAM;QAC1B8R,SAAS,EAAEjS,SAAS,CAACE,KAAK;QAC1BgS,WAAW,EAAE,CAAAhT,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6G,IAAI,KAAI,gBAAgB;QACtDoM,eAAe,EAAEnS,SAAS,CAACK,IAAI,IAAI,SAAS;QAC5C+R,MAAM,EAAE;UACN/R,IAAI,EAAE8J,MAAM,CAAC2B,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG3B,MAAM,CAAC2B,UAAU,IAAI,IAAI,GAAG,QAAQ,GAAG,SAAS;UAC5FuG,gBAAgB,EAAE,GAAGlI,MAAM,CAACmI,MAAM,CAAC3K,KAAK,IAAIwC,MAAM,CAACmI,MAAM,CAACzK,MAAM,EAAE;UAClE0K,EAAE,EAAErJ,SAAS,CAACsJ,QAAQ;UACtBC,OAAO,EAAEvJ,SAAS,CAACwJ,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UAC7CF,SAAS,EAAExJ,SAAS,CAACwJ;QACvB,CAAC;QACDtI,QAAQ,EAAE;UACRyI,QAAQ,EAAEC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;UAC1DC,QAAQ,EAAEhV,QAAQ,CAACgV,QAAQ;UAC3BnB,MAAM,EAAEA;QACV,CAAC;QACDnR,eAAe,EAAE;UACfE,sBAAsB,EAAE,IAAI;UAC5BK,iBAAiB,EAAE,KAAK;UACxBF,oBAAoB,EAAE,KAAK;UAC3BD,wBAAwB,EAAE,KAAK;UAC/BD,eAAe,EAAE,CAAC;UAClBoS,gBAAgB,EAAE,CAAC;YACjBlB,SAAS,EAAEjS,SAAS,CAACE,KAAK;YAC1BkT,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,GAAGxS;QACL,CAAC;QACD8B,kBAAkB,EAAE;UAClB2Q,YAAY,EAAE,IAAI;UAClBC,gBAAgB,EAAE,EAAE;UACpBC,MAAM,EAAE,EAAE;UACV,GAAG7Q;QACL,CAAC;QACDa,cAAc;QACdO;MACF,CAAC;MAED+C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkL,WAAW,CAAC;MAE3D,MAAMwB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIxJ,MAAM,CAACC,QAAQ,CAACwJ,MAAM;MACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,wBAAwB;MAErE,MAAMpC,QAAQ,GAAG,MAAMC,KAAK,CAACkC,MAAM,EAAE;QACnCE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnC,WAAW;MAClC,CAAC,CAAC;MAEF,IAAIN,QAAQ,CAAC0C,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAM3C,QAAQ,CAACE,IAAI,CAAC,CAAC;QACpCrR,YAAY,CAAC8T,MAAM,CAAC/T,SAAS,CAAC;QAC9BG,mBAAmB,CAAC,IAAI6P,IAAI,CAAC,CAAC,CAAC;;QAE/B;QACA,MAAM+C,YAAY,GAAGxC,WAAW,CAACN,GAAG,CAAC,CAAC,GAAGuB,iBAAiB;QAC1DwC,sBAAsB,CAAC,cAAc,EAAEjB,YAAY,CAAC;QAEpDxM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuN,MAAM,CAAC/T,SAAS,CAAC;MAC7D,CAAC,MAAM;QACL,MAAMiU,SAAS,GAAG,MAAM7C,QAAQ,CAACE,IAAI,CAAC,CAAC;QACvC/K,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEwN,SAAS,CAAC;QAC9DC,UAAU,CAAC,eAAe,EAAED,SAAS,CAACE,OAAO,CAAC;MAChD;IACF,CAAC,CAAC,OAAO1N,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DyN,UAAU,CAAC,eAAe,EAAEzN,KAAK,CAAC0N,OAAO,CAAC;IAC5C;EACF,CAAC;EAED,MAAMvE,gBAAgB,GAAGA,CAAC7P,IAAI,EAAE6H,IAAI,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAMwM,WAAW,GAAG;MAClBrU,IAAI;MACJ8B,SAAS,EAAE,IAAImO,IAAI,CAAC,CAAC;MACrBpI;IACF,CAAC;;IAED;IACA,IAAI7H,IAAI,KAAK,aAAa,EAAE;MAC1BsU,oBAAoB,CAAC,mBAAmB,EAAE,IAAI,CAAC;IACjD,CAAC,MAAM,IAAItU,IAAI,KAAK,gBAAgB,EAAE;MACpCsU,oBAAoB,CAAC,sBAAsB,EAAE,IAAI,CAAC;IACpD,CAAC,MAAM,IAAItU,IAAI,KAAK,oBAAoB,EAAE;MACxCsU,oBAAoB,CAAC,0BAA0B,EAAE,IAAI,CAAC;IACxD,CAAC,MAAM,IAAItU,IAAI,KAAK,gBAAgB,EAAE;MACpCsU,oBAAoB,CAAC,iBAAiB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAC7D;;IAEA;IACAuJ,WAAW,CAACtE,QAAQ,GAAGlI,IAAI,CAACkI,QAAQ,IAAI;MAAElB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtDuF,WAAW,CAACE,OAAO,GAAG1M,IAAI,CAAC0M,OAAO,IAAI,EAAE;IACxCF,WAAW,CAACtB,QAAQ,GAAGlL,IAAI,CAACkL,QAAQ,IAAI,CAAC;IACzCsB,WAAW,CAACG,SAAS,GAAG3M,IAAI,CAAC2M,SAAS,IAAI,CAAC;IAC3CH,WAAW,CAACI,QAAQ,GAAGpU,YAAY,CAAC0H,MAAM,GAAG,CAAC;IAC9CsM,WAAW,CAACK,OAAO,GAAG7M,IAAI,CAAC6M,OAAO,IAAI,EAAE;;IAExC;IACA,IAAI1U,IAAI,KAAK,YAAY,EAAE;MACzBsU,oBAAoB,CAAC,yBAAyB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;MACnEpH,mBAAmB,CAACoH,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACPjH,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAI7D,IAAI,KAAK,OAAO,EAAE;MAC3BsU,oBAAoB,CAAC,oBAAoB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;MAC9DpH,mBAAmB,CAACoH,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACPhH,YAAY,EAAE;MAChB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAI9D,IAAI,KAAK,MAAM,EAAE;MAC1BsU,oBAAoB,CAAC,mBAAmB,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAC/D,CAAC,MAAM,IAAI9K,IAAI,KAAK,iBAAiB,EAAE;MACrCsU,oBAAoB,CAAC,6BAA6B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACzE,CAAC,MAAM,IAAI9K,IAAI,KAAK,cAAc,EAAE;MAClCsU,oBAAoB,CAAC,0BAA0B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACtE,CAAC,MAAM,IAAI9K,IAAI,KAAK,gBAAgB,EAAE;MACpCsU,oBAAoB,CAAC,8BAA8B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAI9K,IAAI,KAAK,SAAS,EAAE;MAC7BsU,oBAAoB,CAAC,2BAA2B,EAAGxJ,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;IACvE;;IAEA;IACA,IAAI,CAACvK,eAAe,CAACE,sBAAsB,IAAIN,gBAAgB,EAAE;MAC/D,MAAMM,sBAAsB,GAAG,CAAC,IAAIwP,IAAI,CAAC,CAAC,GAAG9P,gBAAgB,IAAI,IAAI;MACrEmU,oBAAoB,CAAC,wBAAwB,EAAE7T,sBAAsB,CAAC;IACxE;;IAEA;IACA,MAAMM,eAAe,GAAGqH,IAAI,CAAC3F,GAAG,CAAC,GAAG,EACjCpC,YAAY,CAAC0H,MAAM,GAAG,CAAC,GACvBxH,eAAe,CAACa,YAAY,CAACK,UAAU,GAAG,EAAG,GAC7ClB,eAAe,CAACa,YAAY,CAACM,KAAK,GAAG,EAAG,GACxCnB,eAAe,CAACS,WAAW,GAAG,GAAI,GAClCT,eAAe,CAACU,cAAc,GAAG,KACpC,CAAC;IAEDT,kBAAkB,CAACsK,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP/J,eAAe,EAAEqH,IAAI,CAACwH,KAAK,CAAC7O,eAAe;IAC7C,CAAC,CAAC,CAAC;IAEHT,eAAe,CAACwK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEuJ,WAAW,CAAC,CAAC;IAC/C7N,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4N,WAAW,CAAC;EAC3D,CAAC;EAED,MAAMJ,sBAAsB,GAAGA,CAACU,MAAM,EAAEC,KAAK,KAAK;IAChD,MAAMzB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIxJ,MAAM,CAACC,QAAQ,CAACwJ,MAAM;IACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BxT,SAAS,cAAc;IAE9FqR,KAAK,CAACkC,MAAM,EAAE;MACZE,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBa,MAAM;QACNC,KAAK;QACL9S,SAAS,EAAE,IAAImO,IAAI,CAAC;MACtB,CAAC;IACH,CAAC,CAAC,CAAC4E,KAAK,CAACnO,KAAK,IAAI;MAChBF,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyN,UAAU,GAAGA,CAACnU,IAAI,EAAEoU,OAAO,KAAK;IACpC,MAAMjB,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIxJ,MAAM,CAACC,QAAQ,CAACwJ,MAAM;IACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BxT,SAAS,QAAQ;IAExFqR,KAAK,CAACkC,MAAM,EAAE;MACZE,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnB9T,IAAI;QACJoU,OAAO;QACPtS,SAAS,EAAE,IAAImO,IAAI,CAAC;MACtB,CAAC;IACH,CAAC,CAAC,CAAC4E,KAAK,CAACnO,KAAK,IAAI;MAChBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4N,oBAAoB,GAAGA,CAACK,MAAM,EAAEC,KAAK,KAAK;IAC9C;IACApU,kBAAkB,CAACsK,IAAI,IAAI;MACzB,MAAMgK,UAAU,GAAG;QAAE,GAAGhK;MAAK,CAAC;;MAE9B;MACA,IAAI6J,MAAM,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGN,MAAM,CAACrC,KAAK,CAAC,GAAG,CAAC;QACzC,IAAIwC,UAAU,CAACE,MAAM,CAAC,EAAE;UACtBF,UAAU,CAACE,MAAM,CAAC,GAAG;YAAE,GAAGF,UAAU,CAACE,MAAM,CAAC;YAAE,CAACC,KAAK,GAAGL;UAAM,CAAC;QAChE;MACF,CAAC,MAAM;QACLE,UAAU,CAACH,MAAM,CAAC,GAAG,OAAOC,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC9J,IAAI,CAAC6J,MAAM,CAAC,CAAC,GAAGC,KAAK;MAChF;MAEA,OAAOE,UAAU;IACnB,CAAC,CAAC;;IAEF;IACA,IAAI7U,SAAS,EAAE;MACb,MAAMkT,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIxJ,MAAM,CAACC,QAAQ,CAACwJ,MAAM;MACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BxT,SAAS,WAAW;MAE3FqR,KAAK,CAACkC,MAAM,EAAE;QACZE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBa,MAAM;UACNC,KAAK,EAAE,OAAOA,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACrU,eAAe,CAACoU,MAAM,CAAC,IAAI,CAAC,CAAC,GAAGC,KAAK;UAChF9S,SAAS,EAAE,IAAImO,IAAI,CAAC;QACtB,CAAC;MACH,CAAC,CAAC,CAAC4E,KAAK,CAACnO,KAAK,IAAI;QAChBF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMsE,mBAAmB,GAAG,MAAAA,CAAOkK,OAAO,GAAG,WAAW,KAAK;IAC3D,IAAI,CAACjV,SAAS,EAAE;IAEhB,IAAI;MACF,MAAMkV,OAAO,GAAG,IAAIlF,IAAI,CAAC,CAAC;MAC1B,MAAM8C,QAAQ,GAAG5S,gBAAgB,GAAGiI,IAAI,CAACgN,KAAK,CAAC,CAACD,OAAO,GAAGhV,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC;MAEvF,MAAMkV,UAAU,GAAG;QACjBF,OAAO;QACPpC,QAAQ;QACRmC,OAAO;QACP7U,YAAY;QACZE,eAAe;QACf8B,kBAAkB;QAClBa,cAAc;QACdO;MACF,CAAC;MAED,MAAM0P,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIxJ,MAAM,CAACC,QAAQ,CAACwJ,MAAM;MACvE,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,0BAA0BxT,SAAS,EAAE;MAElF,MAAMoR,QAAQ,GAAG,MAAMC,KAAK,CAACkC,MAAM,EAAE;QACnCE,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACuB,UAAU;MACjC,CAAC,CAAC;MAEF,IAAIhE,QAAQ,CAAC0C,EAAE,EAAE;QACfvN,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyO,OAAO,EAAE,WAAW,EAAEnC,QAAQ,EAAE,SAAS,CAAC;MACpF,CAAC,MAAM;QACL,MAAMmB,SAAS,GAAG,MAAM7C,QAAQ,CAACE,IAAI,CAAC,CAAC;QACvC/K,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEwN,SAAS,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOxN,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;;EAMD;EACA,MAAM4O,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC9W,QAAQ,CAAC2K,OAAO,IAAI,CAACzK,SAAS,CAACyK,OAAO,EAAE,OAAO,KAAK;IAEzD,MAAMH,KAAK,GAAGxK,QAAQ,CAAC2K,OAAO;IAC9B,MAAMhC,MAAM,GAAGzI,SAAS,CAACyK,OAAO;IAChC,MAAM/B,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAF,MAAM,CAACG,KAAK,GAAG0B,KAAK,CAACS,UAAU;IAC/BtC,MAAM,CAACK,MAAM,GAAGwB,KAAK,CAACU,WAAW;;IAEjC;IACAtC,GAAG,CAACM,SAAS,CAACsB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE7B,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;;IAEvD;IACA,MAAM+N,cAAc,GAAGvM,KAAK,CAACwM,aAAa;IAC1C,MAAMC,aAAa,GAAGF,cAAc,CAACG,qBAAqB,CAAC,CAAC;;IAE5D;IACA,MAAMC,WAAW,GAAG3M,KAAK,CAACS,UAAU,GAAGT,KAAK,CAACU,WAAW;IACxD,MAAMkM,eAAe,GAAGH,aAAa,CAACnO,KAAK,GAAGmO,aAAa,CAACjO,MAAM;IAElE,IAAIqO,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,OAAO;IAEjD,IAAIL,WAAW,GAAGC,eAAe,EAAE;MACjC;MACAE,aAAa,GAAGL,aAAa,CAACjO,MAAM;MACpCqO,YAAY,GAAGC,aAAa,GAAGH,WAAW;MAC1CI,OAAO,GAAG,CAACF,YAAY,GAAGJ,aAAa,CAACnO,KAAK,IAAI,CAAC;MAClD0O,OAAO,GAAG,CAAC;IACb,CAAC,MAAM;MACL;MACAH,YAAY,GAAGJ,aAAa,CAACnO,KAAK;MAClCwO,aAAa,GAAGD,YAAY,GAAGF,WAAW;MAC1CI,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAACF,aAAa,GAAGL,aAAa,CAACjO,MAAM,IAAI,CAAC;IACtD;;IAEA;IACA;IACA;IACA;;IAEA,MAAMyO,MAAM,GAAG9O,MAAM,CAACG,KAAK,GAAGuO,YAAY;IAC1C,MAAMK,MAAM,GAAG/O,MAAM,CAACK,MAAM,GAAGsO,aAAa;;IAE5C;IACA,MAAMK,KAAK,GAAG/N,IAAI,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAImT,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC1E,MAAMG,KAAK,GAAGhO,IAAI,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIoT,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC;IAC3E,MAAMG,SAAS,GAAGjO,IAAI,CAAC3F,GAAG,CAAC0E,MAAM,CAACG,KAAK,GAAG6O,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,YAAY,GAAII,MAAM,CAAC;IACvF,MAAMK,UAAU,GAAGlO,IAAI,CAAC3F,GAAG,CAAC0E,MAAM,CAACK,MAAM,GAAG4O,KAAK,EAAI,GAAG,GAAG,GAAG,GAAIN,aAAa,GAAII,MAAM,CAAC;;IAE1F;IACA,MAAMK,OAAO,GAAGnO,IAAI,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAImT,YAAY,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC9E,MAAMO,OAAO,GAAGpO,IAAI,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAE,GAAG,GAAG,GAAG,GAAIoT,aAAa,GAAGE,OAAO,IAAIE,MAAM,CAAC,CAAC,CAAC;IAC/E,MAAMO,WAAW,GAAGrO,IAAI,CAAC3F,GAAG,CAAC0E,MAAM,CAACG,KAAK,GAAGiP,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,YAAY,GAAII,MAAM,CAAC,CAAC,CAAC;IAC7F,MAAMS,YAAY,GAAGtO,IAAI,CAAC3F,GAAG,CAAC0E,MAAM,CAACK,MAAM,GAAGgP,OAAO,EAAI,GAAG,GAAG,GAAG,GAAIV,aAAa,GAAII,MAAM,CAAC,CAAC,CAAC;;IAEhG,IAAI;MACF;MACA,MAAMS,aAAa,GAAGvP,GAAG,CAACQ,YAAY,CAACuO,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,CAAC;MAC3E,MAAMM,QAAQ,GAAGD,aAAa,CAAC9O,IAAI;;MAEnC;MACA,MAAMgP,eAAe,GAAGzP,GAAG,CAACQ,YAAY,CAAC2O,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;MACrF,MAAMI,UAAU,GAAGD,eAAe,CAAChP,IAAI;MAEvC,IAAIkP,cAAc,GAAG,CAAC;MACtB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,iBAAiB,GAAG,CAAC;;MAEzB;MACA,MAAMC,UAAU,GAAGA,CAACnP,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAC9B;QACA,MAAMkP,UAAU,GAAGpP,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGE,CAAC,IAAIE,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QACvF,MAAMoP,UAAU,GAAGrP,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIE,IAAI,CAACC,GAAG,CAACL,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE,IAAIG,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;QAChG,MAAMoP,UAAU,GAAGtP,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,GAAGC,CAAC;QAChE,MAAMqP,UAAU,GAAGvP,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,GAAG,IAAIC,CAAC,GAAG,EAAE,IAAIF,CAAC,GAAGC,CAAC,IAAIA,CAAC,IAAIC,CAAC;QAElE,OAAOkP,UAAU,IAAIC,UAAU,IAAIC,UAAU,IAAIC,UAAU;MAC7D,CAAC;;MAED;MACA,KAAK,IAAIzP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8O,QAAQ,CAAC7O,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAME,CAAC,GAAG4O,QAAQ,CAAC9O,CAAC,CAAC;QACrB,MAAMG,CAAC,GAAG2O,QAAQ,CAAC9O,CAAC,GAAG,CAAC,CAAC;QACzB,MAAMI,CAAC,GAAG0O,QAAQ,CAAC9O,CAAC,GAAG,CAAC,CAAC;QAEzB,IAAIqP,UAAU,CAACnP,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvB6O,cAAc,EAAE;QAClB;QACAC,eAAe,EAAE;MACnB;;MAEA;MACA,KAAK,IAAIlP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgP,UAAU,CAAC/O,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAME,CAAC,GAAG8O,UAAU,CAAChP,CAAC,CAAC;QACvB,MAAMG,CAAC,GAAG6O,UAAU,CAAChP,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAMI,CAAC,GAAG4O,UAAU,CAAChP,CAAC,GAAG,CAAC,CAAC;QAE3B,IAAIqP,UAAU,CAACnP,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvB+O,gBAAgB,EAAE;QACpB;QACAC,iBAAiB,EAAE;MACrB;;MAEA;MACA,MAAMM,aAAa,GAAGT,cAAc,GAAGC,eAAe;MACtD,MAAMS,eAAe,GAAGR,gBAAgB,GAAGC,iBAAiB;;MAE5D;MACA;MACA;MACA,MAAMQ,eAAe,GAAGF,aAAa,GAAG,IAAI;MAC5C,MAAMG,cAAc,GAAGF,eAAe,GAAG,IAAI;MAE7C,OAAOC,eAAe,IAAIC,cAAc;IAE1C,CAAC,CAAC,OAAOjR,KAAK,EAAE;MACdF,OAAO,CAACG,IAAI,CAAC,uBAAuB,EAAED,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMkR,2BAA2B,GAAGA,CAACC,WAAW,EAAExR,WAAW,KAAK;IAChE;IACAvH,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMsP,SAAS,GAAG3I,OAAO,CAACqS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpS,IAAI,KAAKkS,WAAW,CAAC;;IAE3D;IACA9L,UAAU,CAAC,MAAM;MACfjN,kBAAkB,CAAC;QACjB6G,IAAI,EAAEkS,WAAW;QACjB5R,QAAQ,EAAE,CAAAmI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEnI,QAAQ,KAAI,EAAE;QAAE;QACrC+R,UAAU,EAAE5J,SAAS,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAM6J,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACxZ,gBAAgB,CAAC0K,OAAO,EAAE;IAE/B,IAAI;MACF;MACA,MAAMhC,MAAM,GAAGtJ,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMoJ,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;MAEnC;MACAF,MAAM,CAACG,KAAK,GAAG7I,gBAAgB,CAAC0K,OAAO,CAAC5B,YAAY,IAAI,GAAG;MAC3DJ,MAAM,CAACK,MAAM,GAAG/I,gBAAgB,CAAC0K,OAAO,CAAC1B,aAAa,IAAI,GAAG;;MAE7D;MACAL,GAAG,CAACM,SAAS,CAACjJ,gBAAgB,CAAC0K,OAAO,EAAE,CAAC,EAAE,CAAC,EAAEhC,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;;MAE1E;MACAL,MAAM,CAAC+Q,MAAM,CAAEC,IAAI,IAAK;QACtB,MAAMC,GAAG,GAAGhO,GAAG,CAACiO,eAAe,CAACF,IAAI,CAAC;QACrC,MAAMG,CAAC,GAAGza,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;QACrCsa,CAAC,CAACC,IAAI,GAAGH,GAAG;QACZE,CAAC,CAACE,QAAQ,GAAG,SAASvI,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;QACtCrS,QAAQ,CAAC+V,IAAI,CAACxV,WAAW,CAACka,CAAC,CAAC;QAC5BA,CAAC,CAACG,KAAK,CAAC,CAAC;QACT5a,QAAQ,CAAC+V,IAAI,CAAC8E,WAAW,CAACJ,CAAC,CAAC;QAC5BlO,GAAG,CAACuO,eAAe,CAACP,GAAG,CAAC;;QAExB;QACAvI,gBAAgB,CAAC,YAAY,EAAE;UAC7B/N,SAAS,EAAE,IAAImO,IAAI,CAAC,CAAC;UACrB2B,SAAS,EAAEjS,SAAS,CAACE,KAAK;UAC1B+Y,eAAe,EAAEzY,gBAAgB,GAAG,CAAC,IAAI8P,IAAI,CAAC,CAAC,GAAG9P,gBAAgB,IAAI,IAAI,GAAG;QAC/E,CAAC,CAAC;;QAEF;QACAuD,mBAAmB,CAACoH,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPjH,cAAc,EAAE;QAClB,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,WAAW,CAAC;IACjB,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CyN,UAAU,CAAC,mBAAmB,EAAEzN,KAAK,CAAC0N,OAAO,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMyE,UAAU,GAAIC,SAAS,IAAK;IAChCjJ,gBAAgB,CAAC,MAAM,EAAE;MACvBiJ,SAAS;MACThX,SAAS,EAAE,IAAImO,IAAI,CAAC,CAAC;MACrBsE,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwE,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,IAAIlQ,SAAS,CAACnH,KAAK,EAAE;QACnB,MAAMmH,SAAS,CAACnH,KAAK,CAAC;UACpBsX,KAAK,EAAE,8BAA8B;UACrCC,IAAI,EAAE,mCAAmC;UACzCb,GAAG,EAAEtO,MAAM,CAACC,QAAQ,CAACwO;QACvB,CAAC,CAAC;QAEF1I,gBAAgB,CAAC,OAAO,EAAE;UACxB6D,MAAM,EAAE,cAAc;UACtB5R,SAAS,EAAE,IAAImO,IAAI,CAAC;QACtB,CAAC,CAAC;QAEFvM,mBAAmB,CAACoH,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPhH,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACA,MAAM+E,SAAS,CAACqQ,SAAS,CAACC,SAAS,CAACrP,MAAM,CAACC,QAAQ,CAACwO,IAAI,CAAC;QACzDa,KAAK,CAAC,2BAA2B,CAAC;QAElCvJ,gBAAgB,CAAC,OAAO,EAAE;UACxB6D,MAAM,EAAE,WAAW;UACnB5R,SAAS,EAAE,IAAImO,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvJ,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCyN,UAAU,CAAC,cAAc,EAAEzN,KAAK,CAAC0N,OAAO,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMiF,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAC9P,aAAa,EAAE;IACpB,IAAI,CAAC5K,UAAU,EAAE;MACf,MAAM2a,eAAe,GAAG9P,YAAY,CAAC,CAAC;MACtC,IAAI/K,gBAAgB,CAAC0K,OAAO,IAAImQ,eAAe,EAAE;QAC/C7a,gBAAgB,CAAC0K,OAAO,CAAC5C,GAAG,GAAG+S,eAAe;QAC9C7a,gBAAgB,CAAC0K,OAAO,CAACrC,KAAK,CAACwC,OAAO,GAAG,OAAO;MAClD;MACA1K,aAAa,CAAC,IAAI,CAAC;MACnB;MACA,IAAI,CAACe,SAAS,CAACE,KAAK,EAAE;QACpBX,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MACAI,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAuQ,gBAAgB,CAAC,SAAS,EAAE;QAC1B6D,MAAM,EAAE,gBAAgB;QACxB6F,UAAU,EAAE,CAAC,CAAC5Z,SAAS,CAACE,KAAK;QAC7BiC,SAAS,EAAE,IAAImO,IAAI,CAAC;MACtB,CAAC,CAAC;;MAEF;MACA,IAAIvR,SAAS,CAACyK,OAAO,IAAI3K,QAAQ,CAAC2K,OAAO,IAAII,aAAa,EAAE;QAC1D,MAAMpC,MAAM,GAAGzI,SAAS,CAACyK,OAAO;QAChC,MAAM/B,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;QACnCF,MAAM,CAACG,KAAK,GAAG9I,QAAQ,CAAC2K,OAAO,CAACM,UAAU;QAC1CtC,MAAM,CAACK,MAAM,GAAGhJ,QAAQ,CAAC2K,OAAO,CAACO,WAAW;QAC5CtC,GAAG,CAACM,SAAS,CAAClJ,QAAQ,CAAC2K,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACrC,MAAMxB,SAAS,GAAGP,GAAG,CAACQ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;QACrExI,cAAc,CAACwJ,qBAAqB,CAACb,SAAS,CAAC,CAAC;MAClD;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAChI,SAAS,CAACE,KAAK,EAAE;QACpBX,uBAAuB,CAAC,IAAI,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAMsa,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI/a,gBAAgB,CAAC0K,OAAO,EAAE;MAC5B1K,gBAAgB,CAAC0K,OAAO,CAACrC,KAAK,CAACwC,OAAO,GAAG,MAAM;IACjD;IACA1K,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,KAAK,CAAC;IACrBE,uBAAuB,CAAC,KAAK,CAAC;IAC9BqF,qBAAqB,CAAC,KAAK,CAAC;IAC5BjF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMma,kBAAkB,GAAIC,MAAM,IAAK;IACrCvV,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtBE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBwL,gBAAgB,CAAC,iBAAiB,EAAE;MAClC7P,IAAI,EAAE,eAAe;MACrB0Z,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAI7Z,IAAI,IAAK;IACtCsE,gBAAgB,CAACtE,IAAI,CAAC;IACtB8P,gBAAgB,CAAC,iBAAiB,EAAE;MAClC7P,IAAI,EAAE,mBAAmB;MACzBD;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAM8Z,eAAe,GAAIC,OAAO,IAAK;IACnC1a,YAAY,CAAC0a,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,QAAQ,GAAG,CAACxV,oBAAoB;IACtCC,uBAAuB,CAACuV,QAAQ,CAAC;;IAEjC;IACA,IAAI,CAACA,QAAQ,EAAE;MACbjV,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACfE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMoV,mBAAmB,GAAGA,CAAA,KAAM;IAChCxV,uBAAuB,CAAC,KAAK,CAAC;IAC9BM,oBAAoB,CAAC,KAAK,CAAC;IAC3BJ,YAAY,CAAC,CAAC,CAAC;IACfE,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,qBAAqB,CAAC,KAAK,CAAC;IAC5BF,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBmV,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO/a,SAAS,KAAK,SAAS,GAAGsG,OAAO,GAAGS,SAAS;EACtD,CAAC;;EAED;EACA,MAAMiU,mBAAmB,GAAIC,OAAO,IAAK;IACvCxC,2BAA2B,CAACwC,OAAO,CAACzU,IAAI,EAAExG,SAAS,CAAC;EACtD,CAAC;;EAED;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACqH,oBAAoB,IAAI7F,UAAU,EAAE;IAEzC,MAAM0b,QAAQ,GAAGnJ,WAAW,CAAC,MAAM;MACjC,MAAMyG,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7CzQ,mBAAmB,CAAC8S,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAAC7S,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAAC4S,cAAc,IAAI7S,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMwM,aAAa,CAACkJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC7V,oBAAoB,EAAE7F,UAAU,EAAEmG,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACAvH,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2H,iBAAiB,IAAInG,UAAU,EAAE;MACpCgG,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAM2V,iBAAiB,GAAGpJ,WAAW,CAAC,MAAM;MAC1CvM,YAAY,CAACmG,IAAI,IAAI;QACnB;QACA,IAAI,CAAClG,gBAAgB,EAAE;UACrBuM,aAAa,CAACmJ,iBAAiB,CAAC;UAChCvV,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAI+F,IAAI,IAAI,CAAC,EAAE;UACb;UACAqG,aAAa,CAACmJ,iBAAiB,CAAC;UAChCvV,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChC4U,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOvO,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMqG,aAAa,CAACmJ,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAACxV,iBAAiB,EAAEnG,UAAU,EAAEiG,gBAAgB,CAAC,CAAC;;EAErD;EACAzH,SAAS,CAAC,MAAM;IACd,IAAI,CAACqH,oBAAoB,IAAI7F,UAAU,EAAE;IAEzC,MAAM0b,QAAQ,GAAGnJ,WAAW,CAAC,MAAM;MACjC,MAAMyG,cAAc,GAAGrC,oBAAoB,CAAC,CAAC;MAC7CzQ,mBAAmB,CAAC8S,cAAc,CAAC;;MAEnC;MACA,IAAIA,cAAc,IAAI,CAAC7S,iBAAiB,IAAIJ,SAAS,KAAK,CAAC,EAAE;QAC3DK,oBAAoB,CAAC,IAAI,CAAC;MAC5B;;MAEA;MACA,IAAI,CAAC4S,cAAc,IAAI7S,iBAAiB,EAAE;QACxCC,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMwM,aAAa,CAACkJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC7V,oBAAoB,EAAE7F,UAAU,EAAEmG,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;;EAEpE;EACAvH,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2H,iBAAiB,IAAInG,UAAU,EAAE;MACpCgG,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACA,IAAI,CAACC,gBAAgB,EAAE;MACrBG,oBAAoB,CAAC,KAAK,CAAC;MAC3BJ,YAAY,CAAC,CAAC,CAAC;MACf;IACF;;IAEA;IACAA,YAAY,CAAC,CAAC,CAAC;IAEf,MAAM2V,iBAAiB,GAAGpJ,WAAW,CAAC,MAAM;MAC1CvM,YAAY,CAACmG,IAAI,IAAI;QACnB;QACA,IAAI,CAAClG,gBAAgB,EAAE;UACrBuM,aAAa,CAACmJ,iBAAiB,CAAC;UAChCvV,oBAAoB,CAAC,KAAK,CAAC;UAC3B,OAAO,CAAC;QACV;QAEA,IAAI+F,IAAI,IAAI,CAAC,EAAE;UACb;UACAqG,aAAa,CAACmJ,iBAAiB,CAAC;UAChCvV,oBAAoB,CAAC,KAAK,CAAC;UAC3BN,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;UAChC4U,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QACA,OAAOvO,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMqG,aAAa,CAACmJ,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAACxV,iBAAiB,EAAEnG,UAAU,EAAEiG,gBAAgB,CAAC,CAAC;;EAIrD;EACA,MAAM2V,gBAAgB,GAAIvL,CAAC,IAAK;IAC9B7J,aAAa,CAAC,IAAI,CAAC;IACnBE,SAAS,CAAC2J,CAAC,CAACwL,OAAO,CAAC,CAAC,CAAC,CAACnL,OAAO,CAAC;EACjC,CAAC;EAED,MAAMoL,eAAe,GAAIzL,CAAC,IAAK;IAC7B,IAAI,CAAC9J,UAAU,EAAE;IAEjB,MAAMwV,QAAQ,GAAG1L,CAAC,CAACwL,OAAO,CAAC,CAAC,CAAC,CAACnL,OAAO;IACrC,MAAMsL,IAAI,GAAGD,QAAQ,GAAGtV,MAAM;;IAE9B;IACA,IAAIuV,IAAI,GAAG,CAAC,EAAE;MACZ1V,gBAAgB,CAAC0V,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC1V,UAAU,EAAE;IAEjBC,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,IAAIH,aAAa,GAAG,GAAG,EAAE;MACvB9F,uBAAuB,CAAC,KAAK,CAAC;IAChC;;IAEA;IACA+F,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA9H,SAAS,CAAC,MAAM;IACd,MAAM0d,kBAAkB,GAAI7L,CAAC,IAAK;MAChC,IAAI1K,kBAAkB,IAAI,CAAC0K,CAAC,CAAC8L,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC7DxW,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAED1G,QAAQ,CAACsN,gBAAgB,CAAC,WAAW,EAAE0P,kBAAkB,CAAC;IAC1Dhd,QAAQ,CAACsN,gBAAgB,CAAC,YAAY,EAAE0P,kBAAkB,CAAC;IAE3D,OAAO,MAAM;MACXhd,QAAQ,CAACuN,mBAAmB,CAAC,WAAW,EAAEyP,kBAAkB,CAAC;MAC7Dhd,QAAQ,CAACuN,mBAAmB,CAAC,YAAY,EAAEyP,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACvW,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAM0W,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,UAAU,GAAG9H,OAAO,CAACC,GAAG,CAAC8H,qBAAqB,IAAI,sBAAsB;MAC9E,MAAMhI,OAAO,GAAG,GAAG+H,UAAU,QAAQ;;MAErC;MACA,IAAIvb,SAAS,CAACE,KAAK,IAAIF,SAAS,CAACG,MAAM,IAAIH,SAAS,CAACI,IAAI,IAAIJ,SAAS,CAACK,IAAI,EAAE;QAC3E,MAAMiK,MAAM,GAAG,IAAIJ,eAAe,CAAC,CAAC;QACpC,IAAIlK,SAAS,CAACE,KAAK,EAAEoK,MAAM,CAACmR,MAAM,CAAC,OAAO,EAAEzb,SAAS,CAACE,KAAK,CAAC;QAC5D,IAAIF,SAAS,CAACG,MAAM,EAAEmK,MAAM,CAACmR,MAAM,CAAC,QAAQ,EAAEzb,SAAS,CAACG,MAAM,CAAC;QAC/D,IAAIH,SAAS,CAACI,IAAI,EAAEkK,MAAM,CAACmR,MAAM,CAAC,MAAM,EAAEzb,SAAS,CAACI,IAAI,CAAC;QACzD,IAAIJ,SAAS,CAACK,IAAI,EAAEiK,MAAM,CAACmR,MAAM,CAAC,MAAM,EAAEzb,SAAS,CAACK,IAAI,CAAC;QACzD,OAAO,GAAGmT,OAAO,IAAIlJ,MAAM,CAACoR,QAAQ,CAAC,CAAC,EAAE;MAC1C;;MAEA;MACA,OAAOlI,OAAO;IAChB,CAAC;IAED,MAAMmI,OAAO,GAAGL,eAAe,CAAC,CAAC;IAEjC,oBACExd,OAAA;MAAKqJ,KAAK,EAAEyU,MAAM,CAACC,gBAAiB;MAAAC,QAAA,eAClChe,OAAA;QAAKqJ,KAAK,EAAEyU,MAAM,CAACG,WAAY;QAAAD,QAAA,gBAC7Bhe,OAAA;UAAIqJ,KAAK,EAAEyU,MAAM,CAACI,OAAQ;UAAAF,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDte,OAAA;UAAGqJ,KAAK,EAAEyU,MAAM,CAACS,UAAW;UAAAP,QAAA,EACzB9b,SAAS,CAACE,KAAK,GACZ,4DAA4D,GAC5D;QAA+E;UAAA+b,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElF,CAAC,eACJte,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAACU,SAAU;UAAAR,QAAA,eAC3Bhe,OAAA,CAACJ,SAAS;YACRuX,KAAK,EAAE0G,OAAQ;YACfvb,IAAI,EAAE,GAAI;YACVmc,KAAK,EAAC,GAAG;YACTC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAC;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNte,OAAA;UAAGqJ,KAAK,EAAEyU,MAAM,CAACc,MAAO;UAAAZ,QAAA,EAAEH;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrCpc,SAAS,CAACG,MAAM,iBACfrC,OAAA;UAAGqJ,KAAK,EAAEyU,MAAM,CAACe,UAAW;UAAAb,QAAA,GAAC,UAAQ,EAAC9b,SAAS,CAACG,MAAM;QAAA;UAAA8b,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAC3D,eACDte,OAAA;UACEqJ,KAAK,EAAEyU,MAAM,CAACgB,OAAQ;UACtBC,OAAO,EAAEle,YAAa;UACtB,cAAW,MAAM;UAAAmd,QAAA,EAClB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,IAAItc,SAAS,EAAE;IACb,oBAAOhC,OAAA,CAACud,aAAa;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;;EAEA;EACA,IAAIxW,cAAc,EAAE;IAClB,oBACE9H,OAAA;MAAKqJ,KAAK,EAAE;QACViJ,QAAQ,EAAE,OAAO;QACjB0M,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPpV,KAAK,EAAE,OAAO;QACdE,MAAM,EAAE,OAAO;QACfmV,UAAU,EAAE,kBAAkB;QAC9BC,MAAM,EAAE,IAAI;QACZtT,OAAO,EAAE,MAAM;QACfuT,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MAAArB,QAAA,eACAhe,OAAA;QAAKqJ,KAAK,EAAE;UACV6V,UAAU,EAAE,OAAO;UACnBI,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,WAAW;UACpBC,QAAQ,EAAE,GAAG;UACb3V,KAAK,EAAE,MAAM;UACb4V,SAAS,EAAE,6BAA6B;UACxCC,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBACAhe,OAAA;UAAIqJ,KAAK,EAAE;YAAEsW,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAA9B,QAAA,EAAC;QAAyB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChHte,OAAA;UAAKqJ,KAAK,EAAE;YAAEsW,KAAK,EAAE,MAAM;YAAEE,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAA9B,QAAA,EAAC;QAE/D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNte,OAAA;UAAKqJ,KAAK,EAAE;YAAEsW,KAAK,EAAE,MAAM;YAAEE,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAA9B,QAAA,EAAC;QAE/D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNte,OAAA;UACEqJ,KAAK,EAAE;YACL6V,UAAU,EAAE,SAAS;YACrBS,KAAK,EAAE,OAAO;YACdI,MAAM,EAAE,MAAM;YACdT,YAAY,EAAE,EAAE;YAChBC,OAAO,EAAE,WAAW;YACpBK,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,EAAE;YACZG,MAAM,EAAE,SAAS;YACjBP,SAAS,EAAE;UACb,CAAE;UACFV,OAAO,EAAEA,CAAA,KAAMhX,iBAAiB,CAAC,KAAK,CAAE;UACxC,cAAW,kBAAkB;UAAAiW,QAAA,EAC9B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEte,OAAA;IAAKqJ,KAAK,EAAEyU,MAAM,CAACmC,SAAU;IAAAjC,QAAA,gBAC3Bhe,OAAA;MAAKqJ,KAAK,EAAEyU,MAAM,CAACoC,eAAgB;MAAAlC,QAAA,gBACjChe,OAAA;QACEmgB,GAAG,EAAEpf,QAAS;QACdsI,KAAK,EAAEyU,MAAM,CAACsC,UAAW;QACzBC,QAAQ;QACRC,WAAW;QACXC,KAAK;QACLC,SAAS,EAAEA,CAAA,KAAMtV,gBAAgB,CAAC,IAAI;MAAE;QAAAiT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACFte,OAAA;QAAQmgB,GAAG,EAAElf,SAAU;QAACoI,KAAK,EAAE;UAAEwC,OAAO,EAAE;QAAO;MAAE;QAAAsS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDte,OAAA;QACEmgB,GAAG,EAAEnf,gBAAiB;QACtBqI,KAAK,EAAEyU,MAAM,CAAC2C,aAAc;QAC5BC,GAAG,EAAC;MAAe;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EAGD,CAACpd,UAAU,iBACVlB,OAAA;QACEqJ,KAAK,EAAEyU,MAAM,CAACgB,OAAQ;QACtBC,OAAO,EAAEA,CAAA,KAAM1S,MAAM,CAACsU,OAAO,CAACC,IAAI,CAAC,CAAE;QACrC,cAAW,MAAM;QAAA5C,QAAA,EAClB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGA,CAACpd,UAAU,iBACVlB,OAAA;QAAKqJ,KAAK,EAAE;UACViJ,QAAQ,EAAE,UAAU;UACpB0M,GAAG,EAAEld,QAAQ,GAAG,EAAE,GAAG,EAAE;UACvB+e,KAAK,EAAE/e,QAAQ,GAAG,EAAE,GAAG,EAAE;UACzBqd,MAAM,EAAE,EAAE;UACVtT,OAAO,EAAE,MAAM;UACfiV,aAAa,EAAE,QAAQ;UACvB1B,UAAU,EAAE,QAAQ;UACpB2B,GAAG,EAAE,KAAK;UACVxB,OAAO,EAAE,MAAM;UACfyB,eAAe,EAAE,oBAAoB;UACrC1B,YAAY,EAAE,MAAM;UACpB2B,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE,YAAY;UAClCzB,SAAS,EAAE,+BAA+B;UAC1CM,MAAM,EAAE;QACV,CAAE;QAAA/B,QAAA,gBACAhe,OAAA;UAAOmhB,SAAS,EAAC,cAAc;UAAAnD,QAAA,EAAC;QAEhC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRte,OAAA;UAAOmhB,SAAS,EAAC,kBAAkB;UAAAnD,QAAA,gBACjChe,OAAA;YACEuC,IAAI,EAAC,UAAU;YACf6e,OAAO,EAAEra,oBAAqB;YAC9Bsa,QAAQ,EAAE/E,uBAAwB;YAClCgF,QAAQ,EAAEja,iBAAkB;YAC5B,cAAW;UAAqB;YAAA8W,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFte,OAAA;YAAMmhB,SAAS,EAAC;UAAe;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAGApd,UAAU,iBACTlB,OAAA;QACEqJ,KAAK,EAAEyU,MAAM,CAACyD,OAAQ;QACtBxC,OAAO,EAAEvC,mBAAoB;QAC7B,cAAW,MAAM;QAAAwB,QAAA,EAClB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAGAjX,iBAAiB,iBAChBrH,OAAA;QAAKqJ,KAAK,EAAEyU,MAAM,CAAC0D,gBAAiB;QAAAxD,QAAA,gBAClChe,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAAC2D,eAAgB;UAAAzD,QAAA,EAAE/W;QAAS;UAAAkX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDte,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAAC4D,aAAc;UAAA1D,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACN,EAGAvX,oBAAoB,IAAI,CAACI,gBAAgB,IAAI,CAACE,iBAAiB,iBAC9DrH,OAAA;QAAKqJ,KAAK,EAAEyU,MAAM,CAAC6D,aAAc;QAAA3D,QAAA,gBAC/Bhe,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAAC8D,UAAW;UAAA5D,QAAA,EAAC;QAA6C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClFte,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAAC+D,aAAc;UAAA7D,QAAA,EAAC;QAAgD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CACN,EAEAvX,oBAAoB,IAAII,gBAAgB,IAAI,CAACE,iBAAiB,iBAC7DrH,OAAA;QAAKqJ,KAAK,EAAEyU,MAAM,CAAC6D,aAAc;QAAA3D,QAAA,eAC/Bhe,OAAA;UAAKqJ,KAAK,EAAE;YAAC,GAAGyU,MAAM,CAAC8D,UAAU;YAAEZ,eAAe,EAAE;UAAyB,CAAE;UAAAhD,QAAA,EAAC;QAEhF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACpd,UAAU,iBACVlB,OAAA;QAAKqJ,KAAK,EAAE;UACViJ,QAAQ,EAAE,UAAU;UACpB0M,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,KAAK;UACX6C,SAAS,EAAE,kBAAkB;UAC7B5C,UAAU,EAAE,kBAAkB;UAC9BS,KAAK,EAAE,OAAO;UACdJ,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,EAAE;UAChBO,QAAQ,EAAE,EAAE;UACZD,UAAU,EAAE,GAAG;UACfT,MAAM,EAAE,EAAE;UACVM,SAAS,EAAE,4BAA4B;UACvCC,SAAS,EAAE,QAAQ;UACnBqC,aAAa,EAAE,GAAG;UAClBvC,QAAQ,EAAE;QACZ,CAAE;QAAAxB,QAAA,EAAC;MAEH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAGA1c,aAAa,iBACZ5B,OAAA;QACEqJ,KAAK,EAAE;UACL,GAAGyU,MAAM,CAACkE,SAAS;UACnBxY,OAAO,EAAEzC,oBAAoB,IAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG;UAC7DmC,MAAM,EAAEvC,oBAAoB,IAAII,gBAAgB,GAC5C,gDAAgD,GAChDJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,iDAAiD,GACjD;QACR,CAAE;QACFga,SAAS,EAAErf,QAAQ,GAAG,mBAAmB,GAAG,EAAG;QAC/C,eAAY,MAAM;QAAAkc,QAAA,eAElBhe,OAAA;UAAKiiB,OAAO,EAAC,aAAa;UAACC,KAAK,EAAC,4BAA4B;UAAAlE,QAAA,gBAE3Dhe,OAAA;YACEmiB,CAAC,EAAC,4EAA4E;YAC9EC,MAAM,EACJrb,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDkb,WAAW,EAAC,GAAG;YACfC,IAAI,EAAC,MAAM;YACXC,aAAa,EAAC;UAAO;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFte,OAAA;YACEmiB,CAAC,EAAC,4EAA4E;YAC9EC,MAAM,EACJrb,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDkb,WAAW,EAAC,GAAG;YACfC,IAAI,EAAC,MAAM;YACXC,aAAa,EAAC;UAAO;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEFte,OAAA;YACEoR,CAAC,EAAC,KAAK;YACPC,CAAC,EAAC,KAAK;YACPxH,KAAK,EAAC,KAAK;YACXE,MAAM,EAAC,KAAK;YACZuY,IAAI,EACFvb,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDqC,OAAO,EAAEzC,oBAAoB,IAAII,gBAAgB,GAAG,MAAM,GAAG,MAAO;YACpEqb,EAAE,EAAC,IAAI;YACPJ,MAAM,EACJrb,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDkb,WAAW,EAAC;UAAG;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEFte,OAAA;YACEyiB,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACRnY,CAAC,EAAC,IAAI;YACN+X,IAAI,EACFvb,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDqC,OAAO,EAAEzC,oBAAoB,IAAII,gBAAgB,GAAG,KAAK,GAAG,KAAM;YAClEib,MAAM,EACJrb,oBAAoB,IAAII,gBAAgB,GACpC,SAAS,GACTJ,oBAAoB,IAAI,CAACI,gBAAgB,GACvC,SAAS,GACT,OACP;YACDkb,WAAW,EAAC;UAAG;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAEDvX,oBAAoB,iBACnB/G,OAAA,CAAAE,SAAA;YAAA8d,QAAA,gBACEhe,OAAA;cAAMoR,CAAC,EAAC,KAAK;cAACC,CAAC,EAAC,KAAK;cAACsR,UAAU,EAAC,QAAQ;cAACL,IAAI,EAAC,OAAO;cAACzC,QAAQ,EAAC,IAAI;cAACD,UAAU,EAAC,MAAM;cAAA5B,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPte,OAAA;cAAMoR,CAAC,EAAC,KAAK;cAACC,CAAC,EAAC,KAAK;cAACsR,UAAU,EAAC,QAAQ;cAACL,IAAI,EAAC,OAAO;cAACzC,QAAQ,EAAC,IAAI;cAACD,UAAU,EAAC,MAAM;cAAA5B,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApd,UAAU,IAAIE,eAAe,iBAC5BpB,OAAA,CAAAE,SAAA;QAAA8d,QAAA,gBAEEhe,OAAA;UAAKqJ,KAAK,EAAE;YACViJ,QAAQ,EAAE,UAAU;YACpB0M,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,KAAK;YACX6C,SAAS,EAAE,kBAAkB;YAC7B5C,UAAU,EAAE,wBAAwB;YACpCS,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,EAAE;YACZP,YAAY,EAAE,EAAE;YAChBC,OAAO,EAAE,UAAU;YACnBJ,MAAM,EAAE,EAAE;YACVM,SAAS,EAAE,iCAAiC;YAC5CC,SAAS,EAAE,QAAQ;YACnBF,QAAQ,EAAE;UACZ,CAAE;UAAAxB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNte,OAAA;UAAKqJ,KAAK,EAAE;YACV,GAAGyU,MAAM,CAAC8E,eAAe;YACzB/Y,KAAK,EAAEnI,SAAS,KAAK,SAAS,GAAG,GAAGwN,WAAW,GAAG,GAAG,GAAGC,cAAc,GAAG;YACzEpF,MAAM,EAAErI,SAAS,KAAK,SAAS,GAC3B,CAAC,MAAM;cACL,MAAMgO,gBAAgB,GAAGnB,kBAAkB;cAC3C,MAAMsU,YAAY,GAAIlc,aAAa,IAAI4H,kBAAmB;cAE1D,IAAIsU,YAAY,EAAE;gBAChB;gBACA,MAAMC,YAAY,GAAG,CAACnc,aAAa,GAAG4H,kBAAkB,IAAIA,kBAAkB;gBAC9E,OAAO,GAAGa,YAAY,IAAI,CAAC,GAAG0T,YAAY,GAAG,GAAG,CAAC,GAAG;cACtD;cACA,OAAO,GAAG1T,YAAY,GAAG;YAC3B,CAAC,EAAE,CAAC,GACJ,GAAGC,eAAe,GAAG;YACzB;YACA0T,QAAQ,EAAE,CAAC,MAAM;cACf,MAAMF,YAAY,GAAIlc,aAAa,IAAI4H,kBAAmB;cAC1D,OAAO7M,SAAS,KAAK,SAAS,IAAImhB,YAAY,GAC1C,gCAAgC,GAChCnhB,SAAS,KAAK,SAAS,IAAIiF,aAAa,GAAG4H,kBAAkB,GAC3D,gCAAgC,GAChC,MAAM;YACd,CAAC,EAAE,CAAC;YACJyU,QAAQ,EAAE;UACZ,CAAE;UAAAhF,QAAA,eACAhe,OAAA;YAAKqJ,KAAK,EAAE;cACViJ,QAAQ,EAAE,UAAU;cACpBzI,KAAK,EAAE,MAAM;cACbE,MAAM,EAAE,MAAM;cACd8B,OAAO,EAAE,MAAM;cACfuT,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAArB,QAAA,gBACAhe,OAAA;cACE8I,GAAG,EAAE,OAAO1H,eAAe,KAAK,QAAQ,GAAGA,eAAe,CAAC8G,IAAI,GAAG9G,eAAgB;cAClFsf,GAAG,EAAC,kBAAkB;cACtBrX,KAAK,EAAE;gBACLQ,KAAK,EAAE,MAAM;gBACbE,MAAM,EAAE,MAAM;gBACdkZ,SAAS,EAAE,SAAS;gBACpBnB,SAAS,EAAEpgB,SAAS,KAAK,WAAW,GAChC,CAAC,MAAM;kBACL;kBACA,MAAMwhB,aAAa,GAAG,uBAAuB7T,eAAe,GAAG,EAAE,GAAG;;kBAEpE;kBACA;kBACA,IAAI/N,WAAW,EAAE;oBACf;oBACA,OAAO,GAAG4hB,aAAa,wBAAwB;kBACjD,CAAC,MAAM;oBACL;oBACA,OAAO,GAAGA,aAAa,aAAa;kBACtC;gBACF,CAAC,EAAE,CAAC,GACJ,CAACC,qBAAA,IAAM;kBACL,MAAMzT,gBAAgB,GAAGf,mBAAmB,CAAClI,UAAU,CAAC;kBACxD,MAAMkJ,iBAAiB,GAAGhF,IAAI,CAAC1F,GAAG,CAAC0B,aAAa,GAAGkI,iBAAiB,EAAEC,uBAAuB,CAAC;kBAC9F,MAAM+T,YAAY,GAAIpc,UAAU,KAAK,KAAK,IAAIkJ,iBAAiB,IAAI,EAAG;kBAEtE,IAAIkT,YAAY,EAAE;oBAChB;oBACA,MAAMC,YAAY,GAAG,CAACnT,iBAAiB,GAAGD,gBAAgB,IAAIA,gBAAgB;oBAC9E,MAAM0T,WAAW,GAAG,CAAC,GAAIN,YAAY,GAAG,GAAI,CAAC,CAAC;oBAC9C,MAAMO,UAAU,GAAG3T,gBAAgB,GAAGC,iBAAiB,CAAC,CAAC;;oBAEzD,OAAO,SAAUP,YAAY,GAAG,EAAE,GAAIiU,UAAU,YAAYA,UAAU,YAAYD,WAAW,GAAG;kBAClG;;kBAEA;kBACA,OAAO,SAASzY,IAAI,CAAC3F,GAAG,CACrBoK,YAAY,GAAG,EAAE,IAAKO,iBAAiB,GAAGD,gBAAgB,GACvDA,gBAAgB,GAAGC,iBAAiB,GACpCD,gBAAgB,GAAGC,iBAAiB,CAAC,EACzC,GAAG,IAAI,EAAAwT,qBAAA,GAAA/hB,eAAe,CAACmZ,UAAU,cAAA4I,qBAAA,uBAA1BA,qBAAA,CAA4B7a,WAAW,KAAI,EAAE,CAAC,CAAC;kBACxD,CAAC,YAAYqH,iBAAiB,GAAGD,gBAAgB,GAC7CA,gBAAgB,GAAGC,iBAAiB,GACpC,CAAC,GAAG;gBACV,CAAC,EAAE,CAAC;gBACRrG,MAAM,EAAE;cACV,CAAE;cACFga,MAAM,EAAG/R,CAAC,IAAK7I,gBAAgB,CAAC6I,CAAC,CAAC8L,MAAM,EAAGnb,SAAS,CAACK,IAAI,KAAK,WAAW,IAAIb,SAAS,KAAK,WAAW,GAAI,UAAU,GAAG,OAAO;YAAE;cAAAyc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI,CAAC,EACD,CAAEpc,SAAS,CAACK,IAAI,KAAK,WAAW,IAAIb,SAAS,KAAK,SAAS,IAAM,CAACQ,SAAS,CAACK,IAAI,IAAIb,SAAS,KAAK,SAAU,KAAK,OAAON,eAAe,KAAK,QAAQ,iBACnJpB,OAAA;cAAKqJ,KAAK,EAAE;gBACViJ,QAAQ,EAAE,UAAU;gBACpBiR,MAAM,EAAE,OAAO;gBACftE,IAAI,EAAE,KAAK;gBACX6C,SAAS,EAAE,kBAAkB;gBAC7BjC,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAE,KAAK;gBACjBD,KAAK,EAAE,OAAO;gBACdqB,eAAe,EAAE,yBAAyB;gBAC1CzB,OAAO,EAAE,SAAS;gBAClBD,YAAY,EAAE,MAAM;gBACpBkE,UAAU,EAAE,QAAQ;gBACpBC,aAAa,EAAE,MAAM;gBACrBhE,SAAS,EAAE,2BAA2B;gBACtCN,MAAM,EAAE;cACV,CAAE;cAAAnB,QAAA,GACC5c,eAAe,CAACoH,QAAQ,IAAIpH,eAAe,CAAC+G,YAAY,IAAIjG,SAAS,CAACI,IAAI,IAAI,IAAI,EAAC,IACpF,EAACqE,aAAa,KAAK4H,kBAAkB,iBACnCvO,OAAA;gBAAMqJ,KAAK,EAAE;kBACXwW,QAAQ,EAAE,MAAM;kBAChBrW,OAAO,EAAE,GAAG;kBACZka,UAAU,EAAE;gBACd,CAAE;gBAAA1F,QAAA,EACC,CAAC,MAAM;kBACN,MAAMvN,cAAc,GAAGlC,kBAAkB,GAAG5H,aAAa;kBAEzD,IAAIgd,iBAAiB;kBACrB,IAAIhd,aAAa,GAAG4H,kBAAkB,EAAE;oBACtC;oBACA,MAAMqV,cAAc,GAAGrV,kBAAkB,GAAG5H,aAAa;oBACzD,MAAMkd,iBAAiB,GAAGtV,kBAAkB,GAAG,IAAI;oBACnD,MAAMuV,iBAAiB,GAAGnZ,IAAI,CAAC3F,GAAG,CAAC4e,cAAc,EAAEC,iBAAiB,CAAC;oBACrE,MAAME,mBAAmB,GAAG,CAAC,GAAID,iBAAiB,GAAGvV,kBAAkB,GAAI,GAAG;oBAC9EoV,iBAAiB,GAAG,CAAC,CAACI,mBAAmB,GAAG,CAAC,IAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;kBAClE,CAAC,MAAM;oBACL;oBACAL,iBAAiB,GAAG,CAAC,CAAClT,cAAc,GAAG,CAAC,IAAI,GAAG,EAAEuT,OAAO,CAAC,CAAC,CAAC;kBAC7D;;kBAEA;kBACA,MAAMC,WAAW,GAAGtd,aAAa,IAAI4H,kBAAkB,GAAG,KAAK,GAAG,EAAE;kBACpE,OAAO,IAAI5H,aAAa,GAAG4H,kBAAkB,GAAG,GAAG,GAAG,EAAE,GAAGoV,iBAAiB,KAAKM,WAAW,EAAE;gBAChG,CAAC,EAAE;cAAC;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EACA,CAACpc,SAAS,CAACK,IAAI,KAAK,WAAW,IAAIb,SAAS,KAAK,WAAW,KAAK,OAAON,eAAe,KAAK,QAAQ,iBACnGpB,OAAA;cAAKqJ,KAAK,EAAE;gBACViJ,QAAQ,EAAE,UAAU;gBACpBiR,MAAM,EAAE,OAAO;gBACftE,IAAI,EAAE,KAAK;gBACX6C,SAAS,EAAE,kBAAkB;gBAC7BjC,QAAQ,EAAE,MAAM;gBAChBD,UAAU,EAAE,KAAK;gBACjBD,KAAK,EAAE,OAAO;gBACdqB,eAAe,EAAE,yBAAyB;gBAC1CzB,OAAO,EAAE,SAAS;gBAClBD,YAAY,EAAE,MAAM;gBACpBkE,UAAU,EAAE,QAAQ;gBACpBC,aAAa,EAAE,MAAM;gBACrBhE,SAAS,EAAE,2BAA2B;gBACtCN,MAAM,EAAE;cACV,CAAE;cAAAnB,QAAA,GACC5c,eAAe,CAAC4L,aAAa,IAAI9K,SAAS,CAACI,IAAI,IAAI,IAAI,EAAC,UAC3D;YAAA;cAAA6b,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH,EAGA,CAACpd,UAAU,iBACVlB,OAAA;QACEqJ,KAAK,EAAEyU,MAAM,CAACoG,UAAW;QACzB/C,SAAS,EAAErf,QAAQ,GAAG,oBAAoB,GAAG,EAAG;QAChDid,OAAO,EAAEnD,aAAc;QACvB,cAAY1a,UAAU,GAAG,iBAAiB,GAAG,SAAU;QACvDogB,QAAQ,EAAE,CAACxV,aAAc;QAAAkS,QAAA,eAEzBhe,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAACqG,YAAa;UAAChD,SAAS,EAAErf,QAAQ,GAAG,qBAAqB,GAAG;QAAG;UAAAqc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CACT,EAEA,CAACxS,aAAa,iBACb9L,OAAA;QAAKqJ,KAAK,EAAE;UACViJ,QAAQ,EAAE,UAAU;UACpB0M,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACX6C,SAAS,EAAE,uBAAuB;UAClC5C,UAAU,EAAE,iBAAiB;UAC7BS,KAAK,EAAE,OAAO;UACdJ,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,EAAE;UAChBO,QAAQ,EAAE,EAAE;UACZD,UAAU,EAAE,GAAG;UACfT,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,EAAC;MAEH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAGApd,UAAU,iBACTlB,OAAA;QACEqJ,KAAK,EAAEyU,MAAM,CAACsG,QAAS;QACvBrF,OAAO,EAAEA,CAAA,KAAM1S,MAAM,CAACC,QAAQ,CAAC+X,MAAM,CAAC,CAAE;QACxC,cAAW,OAAO;QAAArG,QAAA,eAElBhe,OAAA;UAAK6J,KAAK,EAAC,IAAI;UAACE,MAAM,EAAC,IAAI;UAACkY,OAAO,EAAC,WAAW;UAACK,IAAI,EAAC,OAAO;UAAAtE,QAAA,eAC1Dhe,OAAA;YAAMmiB,CAAC,EAAC;UAAyN;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLpd,UAAU,iBACTlB,OAAA;MACEqJ,KAAK,EAAEyU,MAAM,CAACwG,oBAAqB;MACnCnD,SAAS,EAAErf,QAAQ,GAAG,YAAY,GAAG,EAAG;MACxCid,OAAO,EAAEA,CAAA,KAAMjY,qBAAqB,CAAC,IAAI,CAAE;MAC3C,cAAW,mBAAmB;MAAAkX,QAAA,gBAE9Bhe,OAAA;QAAK6J,KAAK,EAAC,IAAI;QAACE,MAAM,EAAC,IAAI;QAACkY,OAAO,EAAC,WAAW;QAACK,IAAI,EAAC,OAAO;QAAAtE,QAAA,eAC1Dhe,OAAA;UAAMmiB,CAAC,EAAC;QAA64B;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACp5B,CAAC,eACNte,OAAA;QAAMqJ,KAAK,EAAEyU,MAAM,CAACyG,aAAc;QAAAvG,QAAA,GAAErX,aAAa,EAAC,IAAE;MAAA;QAAAwX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACT,EAGAzX,kBAAkB,iBACjB7G,OAAA;MACEqJ,KAAK,EAAEyU,MAAM,CAAC0G,YAAa;MAC3BzF,OAAO,EAAEA,CAAA,KAAMjY,qBAAqB,CAAC,KAAK,CAAE;MAC5Cqa,SAAS,EAAC,eAAe;MAAAnD,QAAA,eAEzBhe,OAAA;QACEqJ,KAAK,EAAEyU,MAAM,CAAC2G,cAAe;QAC7B1F,OAAO,EAAGxN,CAAC,IAAKA,CAAC,CAACmT,eAAe,CAAC,CAAE;QACpCvD,SAAS,EAAC,eAAe;QAAAnD,QAAA,gBAEzBhe,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAAC6G,WAAY;UAAA3G,QAAA,gBAC7Bhe,OAAA;YAAIqJ,KAAK,EAAEyU,MAAM,CAAC8G,UAAW;YAAA5G,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDte,OAAA;YACEqJ,KAAK,EAAEyU,MAAM,CAAC+G,aAAc;YAC5B9F,OAAO,EAAEA,CAAA,KAAMjY,qBAAqB,CAAC,KAAK,CAAE;YAC5C,cAAW,OAAO;YAAAkX,QAAA,eAElBhe,OAAA;cAAK6J,KAAK,EAAC,IAAI;cAACE,MAAM,EAAC,IAAI;cAACkY,OAAO,EAAC,WAAW;cAACK,IAAI,EAAC,cAAc;cAAAtE,QAAA,eACjEhe,OAAA;gBAAMmiB,CAAC,EAAC;cAAuG;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENte,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAACgH,YAAa;UAAA9G,QAAA,gBAE9Bhe,OAAA;YAAKqJ,KAAK,EAAEyU,MAAM,CAACiH,eAAgB;YAAA/G,QAAA,eACjChe,OAAA;cACEqJ,KAAK,EAAE;gBACL,GAAGyU,MAAM,CAACkH,YAAY;gBACtB,GAAGlH,MAAM,CAACmH;cACZ,CAAE;cACFlG,OAAO,EAAEA,CAAA,KAAM/C,kBAAkB,CAAC,KAAK,CAAE;cAAAgC,QAAA,EAC1C;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNte,OAAA;YAAKqJ,KAAK,EAAEyU,MAAM,CAACoH,eAAgB;YAAAlH,QAAA,gBACjChe,OAAA;cAAOqJ,KAAK,EAAEyU,MAAM,CAACqH,WAAY;cAAAnH,QAAA,GAAC,cACpB,EAACrX,aAAa,EAAC,IAC7B;YAAA;cAAAwX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRte,OAAA;cACEuC,IAAI,EAAC,OAAO;cACZyC,GAAG,EAAE,EAAG;cACRC,GAAG,EAAE,EAAG;cACRkS,KAAK,EAAExQ,aAAc;cACrB0a,QAAQ,EAAG9P,CAAC,IAAK4K,qBAAqB,CAACrP,QAAQ,CAACyE,CAAC,CAAC8L,MAAM,CAAClG,KAAK,CAAC,CAAE;cACjE9N,KAAK,EAAEyU,MAAM,CAACsH;YAAO;cAAAjH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACFte,OAAA;cAAKqJ,KAAK,EAAEyU,MAAM,CAACuH,YAAa;cAAArH,QAAA,gBAC9Bhe,OAAA;gBAAAge,QAAA,EAAM;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjBte,OAAA;gBAAAge,QAAA,EAAM;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGNte,OAAA;cAAKqJ,KAAK,EAAEyU,MAAM,CAACwH,aAAc;cAAAtH,QAAA,gBAC/Bhe,OAAA;gBACEqJ,KAAK,EAAEyU,MAAM,CAACyH,YAAa;gBAC3BxG,OAAO,EAAEA,CAAA,KAAM5C,qBAAqB,CAAC5N,kBAAkB,CAAE;gBAAAyP,QAAA,GAC1D,WACU,EAACzP,kBAAkB,EAAC,KAC/B;cAAA;gBAAA4P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTte,OAAA;gBACEqJ,KAAK,EAAEyU,MAAM,CAACyH,YAAa;gBAC3BxG,OAAO,EAAEA,CAAA,KAAM5C,qBAAqB,CAAC,EAAE,CAAE;gBAAA6B,QAAA,EAC1C;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTte,OAAA;gBACEqJ,KAAK,EAAEyU,MAAM,CAACyH,YAAa;gBAC3BxG,OAAO,EAAEA,CAAA,KAAM5C,qBAAqB,CAAC,EAAE,CAAE;gBAAA6B,QAAA,EAC1C;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA9c,oBAAoB,iBACnBxB,OAAA;MACEmgB,GAAG,EAAEtY,QAAS;MACdwB,KAAK,EAAE;QACL,GAAGyU,MAAM,CAAC0H,gBAAgB;QAC1B1D,SAAS,EAAE,cAAcva,aAAa,KAAK;QAC3Cke,WAAW,EAAE;MACf,CAAE;MACFtE,SAAS,EAAErf,QAAQ,GAAG,sBAAsB,GAAG,EAAG;MAClD,cAAW,MAAM;MACjB4jB,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAE7I,gBAAiB;MAC/B8I,WAAW,EAAE5I,eAAgB;MAC7B6I,UAAU,EAAE1I,cAAe;MAAAa,QAAA,gBAE3Bhe,OAAA;QACEqJ,KAAK,EAAEyU,MAAM,CAACgI,UAAW;QACzB,eAAY,MAAM;QAClBH,YAAY,EAAE7I,gBAAiB;QAC/B8I,WAAW,EAAE5I,eAAgB;QAC7B6I,UAAU,EAAE1I;MAAe;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,EAED,CAACpc,SAAS,CAACK,IAAI,iBACdvC,OAAA;QAAKqJ,KAAK,EAAEyU,MAAM,CAACiI,WAAY;QAAA/H,QAAA,gBAC7Bhe,OAAA;UACEqJ,KAAK,EAAE;YACL,GAAGyU,MAAM,CAACkI,GAAG;YACb,IAAItkB,SAAS,KAAK,SAAS,GAAGoc,MAAM,CAACpc,SAAS,GAAG,CAAC,CAAC;UACrD,CAAE;UACFqd,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,SAAS,CAAE;UAAA4B,QAAA,EAC3C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTte,OAAA;UACEqJ,KAAK,EAAE;YACL,GAAGyU,MAAM,CAACkI,GAAG;YACb,IAAItkB,SAAS,KAAK,WAAW,GAAGoc,MAAM,CAACpc,SAAS,GAAG,CAAC,CAAC;UACvD,CAAE;UACFqd,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,WAAW,CAAE;UAAA4B,QAAA,EAC7C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eACDte,OAAA;QAAKqJ,KAAK,EAAEyU,MAAM,CAACmI,aAAc;QAAC9E,SAAS,EAAC,gBAAgB;QAAAnD,QAAA,EACzDvB,kBAAkB,CAAC,CAAC,CAACnS,MAAM,GAAG,CAAC,GAC9BmS,kBAAkB,CAAC,CAAC,CAACyJ,GAAG,CAAC,CAACvJ,OAAO,EAAEwJ,KAAK,KAAK;UAC3C;UACA,IAAI,CAACxJ,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMyJ,UAAU,GAAG,CAAC,OAAOhlB,eAAe,KAAK,QAAQ,GAAGA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8G,IAAI,GAAG9G,eAAe,MAAMub,OAAO,CAACzU,IAAI;UAEnH,oBACElI,OAAA;YAEEqJ,KAAK,EAAE;cACL,GAAGyU,MAAM,CAACuI,WAAW;cACrBC,WAAW,EAAEF,UAAU,GAAG,SAAS,GAAG,SAAS;cAC/CpF,eAAe,EAAEoF,UAAU,GAAG,SAAS,GAAG;YAC5C,CAAE;YACF7K,KAAK,EAAE,GAAGoB,OAAO,CAAC1U,IAAI,MAAM0U,OAAO,CAACxU,YAAY,IAAI,KAAK,IAAK;YAC9D4W,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAACC,OAAO,CAAE;YAC5C,cAAY,UAAUA,OAAO,CAAC1U,IAAI,IAAI0U,OAAO,CAACxU,YAAY,IAAI,KAAK,IAAK;YAAA6V,QAAA,gBAExEhe,OAAA;cACE8I,GAAG,EAAE6T,OAAO,CAACzU,IAAK;cAClBwY,GAAG,EAAE/D,OAAO,CAAC1U,IAAK;cAClBoB,KAAK,EAAEyU,MAAM,CAACyI,YAAa;cAC3BC,OAAO,EAAGjV,CAAC,IAAK;gBACdA,CAAC,CAAC8L,MAAM,CAACtF,aAAa,CAAC1O,KAAK,CAACwC,OAAO,GAAG,MAAM;cAC/C;YAAE;cAAAsS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFte,OAAA;cAAKqJ,KAAK,EAAEyU,MAAM,CAAC2I,YAAa;cAAAzI,QAAA,gBAC9Bhe,OAAA;gBAAKqJ,KAAK,EAAEyU,MAAM,CAAC1J,WAAY;gBAAA4J,QAAA,EAAErB,OAAO,CAAC1U;cAAI;gBAAAkW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnD5c,SAAS,KAAK,SAAS,IAAIib,OAAO,CAACxU,YAAY,iBAC9CnI,OAAA;gBAAKqJ,KAAK,EAAEyU,MAAM,CAAC4I,WAAY;gBAAA1I,QAAA,GAAErB,OAAO,CAACxU,YAAY,EAAC,IAAE;cAAA;gBAAAgW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAC9D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAvBD6H,KAAK;YAAAhI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CAAC;QAEb,CAAC,CAAC,gBAEFte,OAAA;UAAKqJ,KAAK,EAAEyU,MAAM,CAAC6I,iBAAkB;UAAA3I,QAAA,gBACnChe,OAAA;YAAKqJ,KAAK,EAAEyU,MAAM,CAAC8I,cAAe;YAAA5I,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3Cte,OAAA;YAAKqJ,KAAK,EAAEyU,MAAM,CAAC+I,eAAgB;YAAA7I,QAAA,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/Dte,OAAA;YAAKqJ,KAAK,EAAEyU,MAAM,CAACgJ,cAAe;YAAA9I,QAAA,EAAC;UAEnC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNte,OAAA;YAAKqJ,KAAK,EAAEyU,MAAM,CAACiJ,iBAAkB;YAAA/I,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAxd,EAAA,CAlxEMF,KAAK;AAAAomB,EAAA,GAALpmB,KAAK;AAmxEX,MAAMkd,MAAM,GAAG;EACbmC,SAAS,EAAE;IACT3N,QAAQ,EAAE,UAAU;IACpBvI,MAAM,EAAE,4BAA4B;IACpC8B,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvBE,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,MAAM;IACbsH,UAAU,EAAE,4EAA4E;IACxFjE,QAAQ,EAAE,QAAQ;IAClByC,WAAW,EAAE,cAAc;IAC3ByB,uBAAuB,EAAE,aAAa;IACtCC,uBAAuB,EAAE,OAAO,CAAC;EACnC,CAAC;EACDjH,eAAe,EAAE;IACfkH,IAAI,EAAE,CAAC;IACP9U,QAAQ,EAAE,UAAU;IACpB0Q,QAAQ,EAAE,QAAQ;IAClBhC,eAAe,EAAE,MAAM;IACvBnV,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDe,UAAU,EAAE;IACVvW,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdkZ,SAAS,EAAE,OAAO;IAClBnB,SAAS,EAAE,WAAW,CAAC;EACzB,CAAC;EACDrB,aAAa,EAAE;IACbnO,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPpV,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdkZ,SAAS,EAAE,OAAO;IAClBpX,OAAO,EAAE,MAAM;IACfwb,eAAe,EAAE,WAAW,CAAC;EAC/B,CAAC;EAEDvI,OAAO,EAAE;IACPxM,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE,MAAM;IACZ+B,eAAe,EAAE,oBAAoB;IACrCrB,KAAK,EAAE,OAAO;IACdJ,OAAO,EAAE,MAAM;IACfD,YAAY,EAAE,KAAK;IACnBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBb,MAAM,EAAE,EAAE;IACVY,MAAM,EAAE,MAAM;IACdN,SAAS,EAAE,+BAA+B;IAC1C6H,UAAU,EAAE,eAAe;IAC3Bzb,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBxV,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdwd,OAAO,EAAE;EACX,CAAC;EACDhG,OAAO,EAAE;IACPjP,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE,MAAM;IACZ+B,eAAe,EAAE,oBAAoB;IACrCrB,KAAK,EAAE,OAAO;IACdJ,OAAO,EAAE,MAAM;IACfD,YAAY,EAAE,KAAK;IACnBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBb,MAAM,EAAE,EAAE;IACVY,MAAM,EAAE,MAAM;IACdN,SAAS,EAAE,+BAA+B;IAC1C6H,UAAU,EAAE,eAAe;IAC3Bzb,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBxV,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdwd,OAAO,EAAE;EACX,CAAC;EACDC,eAAe,EAAE;IACflV,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,MAAM;IACX6B,KAAK,EAAE,MAAM;IACb1B,MAAM,EAAE,EAAE;IACVtT,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvB1B,UAAU,EAAE,QAAQ;IACpB2B,GAAG,EAAE,KAAK;IACVxB,OAAO,EAAE,MAAM;IACfyB,eAAe,EAAE,oBAAoB;IACrC1B,YAAY,EAAE,MAAM;IACpB2B,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCzB,SAAS,EAAE,+BAA+B;IAC1CM,MAAM,EAAE;EACV,CAAC;EACD0H,WAAW,EAAE;IACXnV,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE,MAAM;IACZpV,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdiX,eAAe,EAAE,oBAAoB;IACrC1B,YAAY,EAAE,MAAM;IACpBS,MAAM,EAAE,oCAAoC;IAC5CZ,MAAM,EAAE,CAAC;IACTmI,UAAU,EAAE;EACd,CAAC;EACDI,YAAY,EAAE;IACZpV,QAAQ,EAAE,UAAU;IACpBzI,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACduV,YAAY,EAAE,KAAK;IACnBS,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,SAAS;IACjBsH,UAAU,EAAE,eAAe;IAC3Bzb,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBF,MAAM,EAAE,EAAE;IACVwI,MAAM,EAAE,KAAK;IACblI,SAAS,EAAE,8BAA8B;IACzC8H,OAAO,EAAE,MAAM;IACf,SAAS,EAAE;MACTzF,SAAS,EAAE;IACb;EACF,CAAC;EACD8F,WAAW,EAAE;IACX/H,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdkI,UAAU,EAAE,8BAA8B;IAC1CC,SAAS,EAAE,KAAK;IAChBvI,OAAO,EAAE,UAAU;IACnBD,YAAY,EAAE,MAAM;IACpB0B,eAAe,EAAE,oBAAoB;IACrCe,aAAa,EAAE;EACjB,CAAC;EACDP,gBAAgB,EAAE;IAChBlP,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACX6C,SAAS,EAAE,uBAAuB;IAClC3C,MAAM,EAAE,EAAE;IACVO,SAAS,EAAE,QAAQ;IACnB+D,aAAa,EAAE,MAAM;IACrBlE,OAAO,EAAE,WAAW;IACpByB,eAAe,EAAE,oBAAoB;IACrC1B,YAAY,EAAE,MAAM;IACpB2B,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCzB,SAAS,EAAE;EACb,CAAC;EACDgC,eAAe,EAAE;IACf5B,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBkI,UAAU,EAAE,8BAA8B;IAC1C/H,YAAY,EAAE,KAAK;IACnBiI,SAAS,EAAE;EACb,CAAC;EACDrG,aAAa,EAAE;IACb7B,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdkI,UAAU,EAAE;EACd,CAAC;EACDlG,aAAa,EAAE;IACbrP,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACX6C,SAAS,EAAE,uBAAuB;IAClC3C,MAAM,EAAE,EAAE;IACVO,SAAS,EAAE,QAAQ;IACnB+D,aAAa,EAAE,MAAM;IACrBlE,OAAO,EAAE,WAAW;IACpBD,YAAY,EAAE,MAAM;IACpB0B,eAAe,EAAE,oBAAoB;IACrCC,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCzB,SAAS,EAAE;EACb,CAAC;EACDmC,UAAU,EAAE;IACV/B,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdkI,UAAU,EAAE,8BAA8B;IAC1C7G,eAAe,EAAE,0BAA0B;IAC3CzB,OAAO,EAAE,WAAW;IACpBD,YAAY,EAAE,MAAM;IACpBQ,YAAY,EAAE,KAAK;IACnBwH,UAAU,EAAE;EACd,CAAC;EACDzF,aAAa,EAAE;IACbhC,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,OAAO;IACdkI,UAAU,EAAE,8BAA8B;IAC1C7G,eAAe,EAAE,oBAAoB;IACrCzB,OAAO,EAAE,UAAU;IACnBD,YAAY,EAAE;EAChB,CAAC;EACD0C,SAAS,EAAE;IACT1P,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACX6C,SAAS,EAAE,uBAAuB;IAClCjY,KAAK,EAAE,KAAK;IACZ2V,QAAQ,EAAE,OAAO;IACjBzV,MAAM,EAAE,MAAM;IACdP,OAAO,EAAE,GAAG;IACZia,aAAa,EAAE,MAAM;IACrBtE,MAAM,EAAE,CAAC;IACT7V,MAAM,EAAE,iDAAiD;IACzD0e,YAAY,EAAE,iDAAiD;IAC/DV,UAAU,EAAE;EACd,CAAC;EACH1E,eAAe,EAAE;IACftQ,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACX6C,SAAS,EAAE,uBAAuB;IAClC3C,MAAM,EAAE,CAAC;IACTtT,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBxV,KAAK,EAAE,MAAM;IAAE;IACfoe,WAAW,EAAE,SAAS;IAAE;IACxBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,OAAO;IAAE;IACpB1E,aAAa,EAAE;EACjB,CAAC;EAECS,UAAU,EAAE;IACV5R,QAAQ,EAAE,UAAU;IACpBiR,MAAM,EAAE,MAAM;IACdtE,IAAI,EAAE,KAAK;IACX6C,SAAS,EAAE,kBAAkB;IAC7BjY,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdiX,eAAe,EAAE,0BAA0B;IAC3C1B,YAAY,EAAE,KAAK;IACnBzT,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBW,MAAM,EAAE,SAAS;IACjBb,MAAM,EAAE,EAAE;IACVmI,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,oCAAoC;IAC5CN,SAAS,EAAE,+BAA+B;IAC1C8H,OAAO,EAAE,MAAM;IACfhI,OAAO,EAAE,CAAC;IACV2H,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDtB,YAAY,EAAE;IACZta,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdiX,eAAe,EAAE,SAAS;IAC1B1B,YAAY,EAAE,KAAK;IACnBgI,UAAU,EAAE;EACd,CAAC;EACDlD,QAAQ,EAAE;IACR9R,QAAQ,EAAE,UAAU;IACpBiR,MAAM,EAAE,MAAM;IACd1C,KAAK,EAAE,MAAM;IACbhX,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdiX,eAAe,EAAE,oBAAoB;IACrC1B,YAAY,EAAE,KAAK;IACnBzT,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBW,MAAM,EAAE,SAAS;IACjBb,MAAM,EAAE,EAAE;IACVmI,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,MAAM;IACdN,SAAS,EAAE,+BAA+B;IAC1C8H,OAAO,EAAE,MAAM;IACfhI,OAAO,EAAE;EACX,CAAC;EAED;EACA+E,oBAAoB,EAAE;IACpBhS,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,MAAM;IACX6B,KAAK,EAAE,MAAM;IACbG,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,OAAO;IACdJ,OAAO,EAAE,WAAW;IACpBD,YAAY,EAAE,MAAM;IACpBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBD,MAAM,EAAE,MAAM;IACdlU,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpB2B,GAAG,EAAE,KAAK;IACVwG,OAAO,EAAE,MAAM;IACfD,UAAU,EAAE,eAAe;IAC3BnI,MAAM,EAAE,EAAE;IACVM,SAAS,EAAE,mCAAmC;IAC9C0I,SAAS,EAAE,MAAM;IACjBD,QAAQ,EAAE,MAAM;IAChBhB,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDlB,aAAa,EAAE;IACb1E,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE;EACd,CAAC;EAED;EACA4E,YAAY,EAAE;IACZlS,QAAQ,EAAE,OAAO;IACjB0M,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACP4B,KAAK,EAAE,CAAC;IACR0C,MAAM,EAAE,CAAC;IACTvC,eAAe,EAAE,oBAAoB;IACrCnV,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBF,MAAM,EAAE,EAAE;IACVI,OAAO,EAAE,MAAM;IACf0B,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjCuE,WAAW,EAAE;EACf,CAAC;EACDhB,cAAc,EAAE;IACdzD,eAAe,EAAE,OAAO;IACxB1B,YAAY,EAAE,MAAM;IACpBzV,KAAK,EAAE,MAAM;IACb2V,QAAQ,EAAE,MAAM;IAChB4I,SAAS,EAAE,MAAM;IACjBpF,QAAQ,EAAE,QAAQ;IAClBvD,SAAS,EAAE,gCAAgC;IAC3C5T,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvB6G,MAAM,EAAE,MAAM;IACdrV,QAAQ,EAAE;EACZ,CAAC;EACDqS,WAAW,EAAE;IACX9Y,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BE,OAAO,EAAE,qBAAqB;IAC9B8I,YAAY,EAAE;EAChB,CAAC;EACDzD,UAAU,EAAE;IACV/E,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBgI,MAAM,EAAE;EACV,CAAC;EACD9C,aAAa,EAAE;IACbhb,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACduV,YAAY,EAAE,KAAK;IACnBS,MAAM,EAAE,MAAM;IACdiB,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,MAAM;IACbK,MAAM,EAAE,SAAS;IACjBnU,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBiI,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE;EACX,CAAC;EACDzC,YAAY,EAAE;IACZvF,OAAO,EAAE,qBAAqB;IAC9B1T,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvBC,GAAG,EAAE,MAAM;IACXuH,SAAS,EAAE,MAAM;IACjBnB,uBAAuB,EAAE,OAAO;IAChCiB,SAAS,EAAE;EACb,CAAC;EAED;EACAG,gBAAgB,EAAE;IAChB1c,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvB1B,UAAU,EAAE,QAAQ;IACpB2B,GAAG,EAAE;EACP,CAAC;EACDyH,cAAc,EAAE;IACd3I,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBgI,MAAM,EAAE,CAAC;IACTjI,SAAS,EAAE;EACb,CAAC;EACD+I,iBAAiB,EAAE;IACjB5I,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbgI,MAAM,EAAE,CAAC;IACTjI,SAAS,EAAE,QAAQ;IACnBgJ,UAAU,EAAE;EACd,CAAC;EACD3D,eAAe,EAAE;IACflZ,OAAO,EAAE,MAAM;IACfkV,GAAG,EAAE,MAAM;IACXlX,KAAK,EAAE,MAAM;IACb2V,QAAQ,EAAE;EACZ,CAAC;EACDwF,YAAY,EAAE;IACZoC,IAAI,EAAE,CAAC;IACP7H,OAAO,EAAE,WAAW;IACpBD,YAAY,EAAE,MAAM;IACpBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBsH,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,mBAAmB;IAC3BiB,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,MAAM;IACb4H,OAAO,EAAE;EACX,CAAC;EACDtC,kBAAkB,EAAE;IAClBjE,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,SAAS;IAChB2G,WAAW,EAAE,SAAS;IACtB7G,SAAS,EAAE;EACb,CAAC;EACDyF,eAAe,EAAE;IACfrb,KAAK,EAAE,MAAM;IACb2V,QAAQ,EAAE,OAAO;IACjB3T,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvBC,GAAG,EAAE;EACP,CAAC;EACDoE,WAAW,EAAE;IACXtF,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,MAAM;IACbD,SAAS,EAAE,QAAQ;IACnB7T,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB0B,GAAG,EAAE;EACP,CAAC;EACD4H,UAAU,EAAE;IACV9I,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBqB,eAAe,EAAE,yBAAyB;IAC1CzB,OAAO,EAAE,SAAS;IAClBD,YAAY,EAAE;EAChB,CAAC;EACD8F,MAAM,EAAE;IACNvb,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,KAAK;IACbuV,YAAY,EAAE,KAAK;IACnBJ,UAAU,EAAE,SAAS;IACrBqI,OAAO,EAAE,MAAM;IACfvH,MAAM,EAAE,SAAS;IACjB4I,gBAAgB,EAAE,MAAM;IACxBC,UAAU,EAAE;EACd,CAAC;EACDxD,YAAY,EAAE;IACZxZ,OAAO,EAAE,MAAM;IACfwT,cAAc,EAAE,eAAe;IAC/BQ,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbmI,SAAS,EAAE;EACb,CAAC;EACDxC,aAAa,EAAE;IACbzZ,OAAO,EAAE,MAAM;IACfkV,GAAG,EAAE,KAAK;IACVlX,KAAK,EAAE,MAAM;IACb2V,QAAQ,EAAE;EACZ,CAAC;EACD+F,YAAY,EAAE;IACZ6B,IAAI,EAAE,CAAC;IACP7H,OAAO,EAAE,UAAU;IACnBD,YAAY,EAAE,KAAK;IACnBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBsH,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,mBAAmB;IAC3BiB,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,MAAM;IACb4H,OAAO,EAAE;EACX,CAAC;EACDuB,cAAc,EAAE;IACdjf,KAAK,EAAE,MAAM;IACb2V,QAAQ,EAAE,OAAO;IACjBD,OAAO,EAAE,WAAW;IACpByB,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,SAAS;IAChBL,YAAY,EAAE,MAAM;IACpBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBsH,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,MAAM;IACdwH,OAAO,EAAE,MAAM;IACf9H,SAAS,EAAE;EACb,CAAC;EAED+F,gBAAgB,EAAE;IAChBlT,QAAQ,EAAE,UAAU;IACpBiR,MAAM,EAAE,CAAC;IACTtE,IAAI,EAAE,CAAC;IACP4B,KAAK,EAAE,CAAC;IACRG,eAAe,EAAE,2BAA2B;IAC5CC,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClC6H,mBAAmB,EAAE,MAAM;IAC3BC,oBAAoB,EAAE,MAAM;IAC5BzJ,OAAO,EAAE,MAAM;IACf6I,SAAS,EAAE,MAAM;IACjBvc,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvB3B,MAAM,EAAE,EAAE;IACVM,SAAS,EAAE,iCAAiC;IAC5CM,MAAM,EAAE,MAAM;IACd+B,SAAS,EAAE,eAAe;IAC1BwF,UAAU,EAAE,yBAAyB;IACrCtE,QAAQ,EAAE,QAAQ;IAClByC,WAAW,EAAE,MAAM;IACnBwD,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,QAAQ,EAAE;IACR9W,QAAQ,EAAE,UAAU;IACpB0M,GAAG,EAAE,MAAM;IACX6B,KAAK,EAAE,MAAM;IACblB,KAAK,EAAE,MAAM;IACbK,MAAM,EAAE,SAAS;IACjBb,MAAM,EAAE,EAAE;IACVtV,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACd8B,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,KAAK;IACnB0B,eAAe,EAAE,oBAAoB;IACrCsG,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,MAAM;IACdwH,OAAO,EAAE,MAAM;IACfhI,OAAO,EAAE;EACX,CAAC;EACDwG,WAAW,EAAE;IACXla,OAAO,EAAE,MAAM;IACfiU,YAAY,EAAE,MAAM;IACpBkB,eAAe,EAAE,SAAS;IAC1B1B,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,KAAK;IACdwB,GAAG,EAAE;EACP,CAAC;EACDiF,GAAG,EAAE;IACHoB,IAAI,EAAE,CAAC;IACP1H,SAAS,EAAE,QAAQ;IACnBH,OAAO,EAAE,WAAW;IACpBD,YAAY,EAAE,KAAK;IACnBO,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBI,MAAM,EAAE,SAAS;IACjBsH,UAAU,EAAE,eAAe;IAC3B3H,KAAK,EAAE,MAAM;IACb4H,OAAO,EAAE,MAAM;IACfxH,MAAM,EAAE,MAAM;IACdiB,eAAe,EAAE,aAAa;IAC9BkG,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACD/jB,SAAS,EAAE;IACTsf,eAAe,EAAE,SAAS;IAC1BrB,KAAK,EAAE,SAAS;IAChBF,SAAS,EAAE;EACb,CAAC;EACDwG,aAAa,EAAE;IACbpa,OAAO,EAAE,MAAM;IACfwd,mBAAmB,EAAE,uCAAuC;IAC5DtI,GAAG,EAAE,MAAM;IACXqH,SAAS,EAAE,oBAAoB;IAC/BE,SAAS,EAAE,MAAM;IACjBgB,aAAa,EAAE,MAAM;IACrBC,cAAc,EAAE,MAAM;IACtBC,cAAc,EAAE,cAAc;IAC9BrC,uBAAuB,EAAE;EAC3B,CAAC;EACDd,WAAW,EAAE;IACX/T,QAAQ,EAAE,UAAU;IACpBzI,KAAK,EAAE,MAAM;IACboe,WAAW,EAAE,KAAK;IAClBjH,eAAe,EAAE,SAAS;IAC1B1B,YAAY,EAAE,MAAM;IACpBzT,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBW,MAAM,EAAE,SAAS;IACjBsH,UAAU,EAAE,eAAe;IAC3BvH,MAAM,EAAE,mBAAmB;IAC3BiD,QAAQ,EAAE,QAAQ;IAClBvD,SAAS,EAAE,+BAA+B;IAC1CF,OAAO,EAAE,KAAK;IACdgI,OAAO,EAAE,MAAM;IACfL,uBAAuB,EAAE,aAAa;IACtCzB,WAAW,EAAE;EACf,CAAC;EACDc,YAAY,EAAE;IACZ1c,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,MAAM;IACdkZ,SAAS,EAAE,SAAS;IACpB3D,YAAY,EAAE,KAAK;IACnB0B,eAAe,EAAE;EACnB,CAAC;EACDyF,YAAY,EAAE;IACZnU,QAAQ,EAAE,UAAU;IACpBiR,MAAM,EAAE,KAAK;IACbtE,IAAI,EAAE,KAAK;IACX4B,KAAK,EAAE,KAAK;IACZhB,QAAQ,EAAE,KAAK;IACfF,KAAK,EAAE,MAAM;IACbD,SAAS,EAAE,QAAQ;IACnBsB,eAAe,EAAE,2BAA2B;IAC5C1B,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,SAAS;IAClByD,QAAQ,EAAE;EACZ,CAAC;EACD5O,WAAW,EAAE;IACXyL,QAAQ,EAAE,KAAK;IACfD,UAAU,EAAE,KAAK;IACjB4D,UAAU,EAAE,QAAQ;IACpBiG,YAAY,EAAE,UAAU;IACxBzG,QAAQ,EAAE,QAAQ;IAClBlD,YAAY,EAAE;EAChB,CAAC;EACD4G,WAAW,EAAE;IACX7G,QAAQ,EAAE,KAAK;IACfD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChB6D,UAAU,EAAE;EACd,CAAC;EACDsC,UAAU,EAAE;IACVjc,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE,KAAK;IACbiX,eAAe,EAAE,SAAS;IAC1B1B,YAAY,EAAE,KAAK;IACnBqI,MAAM,EAAE,aAAa;IACrB3H,MAAM,EAAE,MAAM;IACdyF,WAAW,EAAE,MAAM;IACnByD,UAAU,EAAE,MAAM;IAClBC,gBAAgB,EAAE;EACpB,CAAC;EAEDpL,gBAAgB,EAAE;IAChBzL,QAAQ,EAAE,UAAU;IACpBvI,MAAM,EAAE,OAAO;IACf8B,OAAO,EAAE,MAAM;IACfuT,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxB2B,eAAe,EAAE,SAAS;IAC1BzB,OAAO,EAAE;EACX,CAAC;EACDtB,WAAW,EAAE;IACX+C,eAAe,EAAE,OAAO;IACxBzB,OAAO,EAAE,MAAM;IACfD,YAAY,EAAE,MAAM;IACpBG,SAAS,EAAE,gCAAgC;IAC3CC,SAAS,EAAE,QAAQ;IACnBF,QAAQ,EAAE,OAAO;IACjB3V,KAAK,EAAE;EACT,CAAC;EACDqU,OAAO,EAAE;IACP2B,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,SAAS;IAChBG,YAAY,EAAE;EAChB,CAAC;EACDvB,UAAU,EAAE;IACVsB,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE,MAAM;IACpB4I,UAAU,EAAE;EACd,CAAC;EACDlK,SAAS,EAAE;IACTwC,eAAe,EAAE,OAAO;IACxBzB,OAAO,EAAE,MAAM;IACfD,YAAY,EAAE,MAAM;IACpBzT,OAAO,EAAE,cAAc;IACvBiU,YAAY,EAAE,MAAM;IACpBL,SAAS,EAAE;EACb,CAAC;EACDb,MAAM,EAAE;IACNiB,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,SAAS;IAChBG,YAAY,EAAE,MAAM;IACpB4J,SAAS,EAAE;EACb,CAAC;EACD7K,UAAU,EAAE;IACVgB,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE,MAAM;IACpB6J,SAAS,EAAE;EACb,CAAC;EACDhD,iBAAiB,EAAE;IACjB9a,OAAO,EAAE,MAAM;IACfiV,aAAa,EAAE,QAAQ;IACvB1B,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBE,OAAO,EAAE,WAAW;IACpBG,SAAS,EAAE,QAAQ;IACnB3V,MAAM,EAAE;EACV,CAAC;EACD6c,cAAc,EAAE;IACd/G,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE;EAChB,CAAC;EACD+G,eAAe,EAAE;IACfhH,QAAQ,EAAE,MAAM;IAChBD,UAAU,EAAE,KAAK;IACjBD,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE;EAChB,CAAC;EACDgH,cAAc,EAAE;IACdjH,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACb+I,UAAU,EAAE,KAAK;IACjB5I,YAAY,EAAE,KAAK;IACnBN,QAAQ,EAAE;EACZ,CAAC;EACDuH,iBAAiB,EAAE;IACjBlH,QAAQ,EAAE,MAAM;IAChBF,KAAK,EAAE,MAAM;IACb+I,UAAU,EAAE,KAAK;IACjBlJ,QAAQ,EAAE;EACZ;AACF,CAAC;AAED,eAAe5e,KAAK;AAAC,IAAAomB,EAAA;AAAA4C,YAAA,CAAA5C,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}